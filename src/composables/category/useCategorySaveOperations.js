import { useNuxtApp } from "#app";
import { useCategoryStore } from "@/stores/category.js";
import { useUserDataStore } from "@/stores/userData.js";

export function useCategorySaveOperations(state, methods) {
  const { $keys } = useNuxtApp();
  const categoryStore = useCategoryStore();
  const userDataStore = useUserDataStore();

  const createCategoryWithVariants = async () => {
    const currentLang = userDataStore.getDefaultLang;

    const defaultVariantData = {
      name: state.categoriesName.value.trim(),
      lang: currentLang,
    };

    const allVariants = [defaultVariantData, ...state.recipeVariantList.value];

    const payload = {
      isin: state.newIsin.value,
      type: "category",
      data: updatedRecipeVariantList(allVariants),
      image: {
        [currentLang]: state.imageResponseUrl.value ? state.imageResponseUrl.value.replace(/\?.*/, "") : state.image.value,
      },
      slug: state.categoriesSlug.value
        ? { [currentLang]: state.categoriesSlug.value }
        : undefined,
    };

    if (!payload.image) delete payload.image;
    if (!payload.slug) delete payload.slug;

    const response = await categoryStore.postCategoryAsync(payload, currentLang);

    if (response?.entity?.isin && state.selectedCategoryRecipe.value.length) {
      state.categoryISIN.value = response.entity.isin;
      await postCategoryRecipeAsync(state.selectedCategoryRecipe.value);

      if (state.categoryPromotedRecipes.value.length) {
        await savePromotedRecipeAsync();
      }
    } else if (response?.entity?.isin) {
      state.categoryISIN.value = response.entity.isin;
    }

    if (state.isPublish.value && response?.entity?.isin) {
      await patchPublishCategoryAsync(response.entity.isin);
    }
  };

  const updateCategoryWithVariants = async () => {
    const currentLang = userDataStore.getDefaultLang;

    const defaultVariantData = {
      name: state.categoriesName.value.trim(),
      lang: currentLang,
    };

    const allVariants = [defaultVariantData, ...state.recipeVariantList.value];

    let payload = {
      isin: state.categoryISIN.value,
      slug: {
        [state.lang.value]: state.categoriesSlug.value?.trim() || "",
      },
      type: "category",
      data: updatedRecipeVariantList(allVariants),
      image: {
        [state.lang.value]: state.imageResponseUrl.value ? state.imageResponseUrl.value.replace(/\?.*/, "") : state.image.value,
      },
    };
    
    payload = await setPayLoadWithVariantAsync(payload);
    payload = await setPayLoadWithVariantSlugAsync(payload);
    
    if (payload?.slug?.[state.lang.value] === "") {
      delete payload.slug;
    }

    await categoryStore.postCategoryAsync(payload, currentLang);

    if (state.saveRemovedCategoryVariants.value?.length) {
      await deleteVariantAsync();
    }

    if (state.selectedCategoryRecipe.value?.length) {
      await postCategoryRecipeAsync(state.selectedCategoryRecipe.value);

      if (state.isAbortedCheckingOperationStatus.value) {
        throw new Error("Operation was aborted");
      }
    }

    await savePromotedRecipeAsync();

    if (state.recipeMatchesIsinsRemove.value.length) {
      await removeCategoryRecipeAsync();

      if (state.isAbortedCheckingOperationStatus.value) {
        throw new Error("Operation was aborted");
      }
    }

    await patchPublishCategoryAsync(state.categoryISIN.value);
  };

  const updatedRecipeVariantList = (variantList) => {
    const updatedVariantList = {};

    variantList.forEach((variant) => {
      if (variant.name && variant.lang) {
        updatedVariantList[variant.lang] = {
          name: variant.name.trim(),
        };
      }
    });
    state.finalSelectedLanguage.value = Object.keys(updatedVariantList);

    return updatedVariantList;
  };

  const setPayLoadWithVariantAsync = async (payload) => {
    if (payload.image?.[state.lang.value]) {
      payload.image = await setLanguageVariant(payload.image);
    }
    return payload;
  };

  const setPayLoadWithVariantSlugAsync = async (payload) => {
    if (payload.slug?.[state.lang.value]) {
      payload.slug = await setLanguageVariant(payload.slug);
    }
    return payload;
  };

  const setLanguageVariant = (variantList) => {
    const copyObjectData = [];
    if (
      state.finalSelectedLanguage.value.length &&
      variantList?.[state.lang.value]
    ) {
      state.finalSelectedLanguage.value.forEach((item) => {
        copyObjectData.push({ [item]: variantList[state.lang.value] });
      });
    }

    return Object.assign({}, ...copyObjectData);
  };

  const postCategoryRecipeAsync = async (recipeIsins) => {
    try {
      const payload = {
        sourceId: $keys.KEY_NAMES.SOURCE_ID,
        data: {
          action: "add",
          isin: state.categoryISIN.value,
          targets: recipeIsins,
        },
      };

      const response = await categoryStore.postCategoryRecipeAsync(payload);

      if (response?.opId) {
        await methods.checkOperationStatusAsync(response.opId);
      }

      return response;
    } catch (error) {
      console.error(
        "[IQ][CategoryForm] Error in postCategoryRecipeAsync:",
        error
      );
      throw error;
    }
  };

  const savePromotedRecipeAsync = async () => {
    const promotedData = state.categoryPromotedRecipes.value
      ? state.categoryPromotedRecipes.value.map((recipe) => recipe.isin)
      : [];

    const payload = {
      isin: state.categoryISIN.value,
      targetIsin: state.categoryISIN.value,
      campaignType: "categoryRecipeSuggestion",
      promotedRecipeIsins: [...new Set(promotedData)],
      filteredRecipeIsins: state.filteredRecipeIsins.value || [],
      preview: false,
    };

    try {
      const currentLang = userDataStore.getDefaultLang;
      await categoryStore.saveRecipeCampaignDataAsync(payload, currentLang);
    } catch (error) {
      console.error("[IQ][CategoryForm] Error saving promoted recipes:", error);
      throw error;
    }
  };

  const removeCategoryRecipeAsync = async () => {
    const payload = {
      sourceId: $keys.KEY_NAMES.SOURCE_ID,
      data: {
        action: "remove",
        isin: state.categoryISIN.value,
        targets: state.recipeMatchesIsinsRemove.value,
      },
    };

    try {
      const response = await categoryStore.postCategoryRecipeAsync(payload);
      const operationId = response.opId;
      await methods.checkOperationStatusAsync(operationId);
    } catch (error) {
      console.error("[IQ][CategoryForm] Error removing category recipes:", error);
      throw error;
    }
  };

  const patchPublishCategoryAsync = async (isin) => {
    if (isin && state.isCategoriesStatus.value) {
      const payload = {
        status: state.isCategoriesStatus.value,
      };
      try {
        await categoryStore.patchCategoryAsync(payload, isin);
      } catch (error) {
        console.error(
          "[IQ][CategoryForm] Error updating category status:",
          error
        );
        throw error;
      }
    }
  };

  const deleteVariantAsync = async () => {
    try {
      await categoryStore.deleteLanguageVariantAsync(state.categoryISIN.value, state.saveRemovedCategoryVariants.value);
    } catch (e) {
      console.error("[IQ][CategoryForm] Error deleting variants:", e);
    }
  };

  const removeAllCategoryAssociationsAsync = async (isin) => {
    try {
      const recipeAssociationPayload = {
        sourceId: $keys.KEY_NAMES.SOURCE_ID,
        data: {
          action: 'removeAll',
          entityType: 'recipe',
          isin: isin,
        },
      };

      const response = await categoryStore.postCategoryRecipeAsync(recipeAssociationPayload);
      if (response?.opId) {
        await waitForOperationCompletion(response.opId);
      }
      const promises = [];

      state.recipeVariantList.value.forEach((variant) => {
        if (variant.lang !== state.lang.value) {
          promises.push(
            categoryStore.deleteLanguageVariantAsync(isin, [variant.lang])
          );
        }
      });

      await Promise.all(promises);
    } catch (error) {
      console.error("[IQ][CategoryForm] Error removing category associations:", error);
      throw error;
    }
  };

  const waitForOperationCompletion = async (operationId) => {
    const maxAttempts = 30;
    let attempts = 0;
    const states = [$keys.KEY_NAMES.DONE, $keys.KEY_NAMES.FAILED];

    while (attempts < maxAttempts) {
      try {
        await categoryStore.getOperationStatusAsync(operationId);
        const status = categoryStore.getOperationStatus;

        if (states.includes(status?.state)) {
          if (status.state === $keys.KEY_NAMES.FAILED) {
            throw new Error(`Operation failed: ${status.message || 'Unknown error'}`);
          }
          return;
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
        attempts++;
      } catch (error) {
        console.error("[IQ][CategoryForm] Error checking operation status:", error);
        throw error;
      }
    }

    throw new Error("Operation timeout: Recipe association removal took too long");
  };

  return {
    createCategoryWithVariants,
    updateCategoryWithVariants,
    updatedRecipeVariantList,
    setPayLoadWithVariantAsync,
    setPayLoadWithVariantSlugAsync,
    setLanguageVariant,
    postCategoryRecipeAsync,
    savePromotedRecipeAsync,
    removeCategoryRecipeAsync,
    patchPublishCategoryAsync,
    deleteVariantAsync,
    removeAllCategoryAssociationsAsync,
    waitForOperationCompletion,
  };
}
