import { useTableManagerStore } from "../stores/table-manager.js";
import { computed, ref } from "vue";
import { KEYS } from "../сonstants/keys.js";
import { useStore } from "vuex";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";
import { useSearchStore } from "../stores/search.js";
import { useRouter } from "vue-router";
import { isAsyncFunction } from "../utils/is-async-function.js";
import { TABLE_MANAGER_STATE } from "../models/table-manager.model.js";
import { useSimpleCustomFetch } from "./useCustomFetch.js";
import { useConfig } from "./useConfig.js";

export const useTableManager = (
  {
    storeId,
    clientKey,
    endpointKey,
    fetchFn,
    defaultParams = {},
    afterMapParamsFn,
    afterFetchFn,
    defaultPagination = {
      from: 0,
      size: 15,
      total: 0,
    },
    immediate = true,
    smoothUpdate = false,
    smoothUpdateInterval = 10000,
    smoothUpdateStates = [KEYS.STATE.PUBLISHING, KEYS.STATE.UNPUBLISHING],
    resultModel,
  }
) => {
  const store = useStore();
  const router = useRouter();
  const route = useRoute();
  const { readyProject } = useProjectLang();
  const { setSearchQuery } = useSearchStore();
  const { config } = useConfig();

  const smoothTimer = ref(null);
  const isSmoothUpdateNeeds = ref(false);
  const isDecrementPageLogicEnabled = ref(true);

  const lang = computed(() => store.getters["userData/getDefaultLang"]);


  const {
    tms_state,
    tms_rows,
    tms_pagination,
    tms_isLoading,
    tms_isFirstLoadCompleted,
    tms_setState,
    tms_setRows,
    tms_setLoading,
    tms_setPaginationFrom,
    tms_setPaginationTotal,
    tms_setFirstLoad,
    tms_reset,
  } = useTableManagerStore({
    storeId,
    defaultPagination: {
      from: _getParamFrom(),
      size: defaultPagination.size,
      total: defaultPagination.total,
    },
  });

  const getParams = () => {
    const { search, searchQuery, editPageQuery, editCategoryQuery, editRecipeQuery, editTagQuery } = route.query;
    const searchQueryString = search || searchQuery || editCategoryQuery || editPageQuery || editRecipeQuery || editTagQuery || undefined;

    return {
      q: searchQueryString || undefined,
      from: _getParamFrom(),
      size: defaultPagination.size,
      includeTotals: true,
      lang: lang.value,
      sort: "lastMod",
      ...defaultParams,
    };
  };

  const applyResultModel = (data, model) => {
    try {
      if (typeof model !== "function") {
        return data;
      }

      if (model.prototype?.constructor) {
        return data.map((item) => new model(item));
      }

      return data.map((item) => model(item));
    } catch (e) {
      console.error("[IQ][Table Manager] Cannot apply the Result Model!", e);
      return data;
    }
  }

  const fetch = async ({ params = {}, isSmoothUpdate = false }) => {
    const completeLoading = () => {
      tms_setLoading(false);
      tms_setFirstLoad(true);
    };

    if (!isSmoothUpdate) {
      tms_setLoading(true);
      tms_setState(TABLE_MANAGER_STATE.LOADING);
    }

    tms_setPaginationFrom(params.from);

    if (fetchFn && typeof fetchFn === "function") {
      try {
        const response = isAsyncFunction(fetchFn)
          ? await fetchFn(params, clientKey, endpointKey)
          : fetchFn(params, clientKey, endpointKey);
        completeLoading();
        tms_setState(TABLE_MANAGER_STATE.CUSTOM_FETCH);
        return response;
      } catch (e) {
        console.error(`[IQ][Table Manager] Cannot fetch data by fetchFn!`, e);
        completeLoading();
        tms_setState(TABLE_MANAGER_STATE.ERROR);
        return e;
      }
    }

    try {
      const response = await useSimpleCustomFetch("", { params }, clientKey, endpointKey);
      if (response) {
        const result = applyResultModel(response.results, resultModel);
        tms_setPaginationTotal(response.total);

        if (smoothUpdate) {
          checkIfNeedSmoothUpdateByStates(result);
        }

        if (result?.length) {
          tms_setRows(result);
          tms_setState(TABLE_MANAGER_STATE.DATA);
        } else if (isDecrementPageLogicEnabled.value && tms_isFirstLoadCompleted.value) {
          decrementPageIfNoResults();
        } else {
          tms_setState(TABLE_MANAGER_STATE.NO_DATA);
        }
      } else {
        tms_setState(TABLE_MANAGER_STATE.EMPTY);
      }
    } catch (e) {
      console.error(`[IQ][Table Manager] Cannot fetch data: clientKey: ${clientKey}, endpointKey: ${endpointKey}.`, e);
      tms_setState(TABLE_MANAGER_STATE.ERROR);
    } finally {
      completeLoading();
    }
  };

  const proxyFetch = async ({ params = {}, isSmoothUpdate = false }) => {
    let paramsObject = Object.keys(params)?.length ? params : getParams();

    if (afterMapParamsFn && typeof afterMapParamsFn === "function") {
      paramsObject = isAsyncFunction(afterMapParamsFn)
        ? await afterMapParamsFn(paramsObject)
        : afterMapParamsFn(paramsObject);
    }

    await fetch({
      params: paramsObject,
      isSmoothUpdate,
    });

    if (afterFetchFn && typeof afterFetchFn === "function") {
      const data = {
        params: paramsObject,
        rows: tms_rows.value,
      };
      isAsyncFunction(afterFetchFn) ? await afterFetchFn(data) : afterFetchFn(data);
    }
  };

  const decrementPageIfNoResults = () => {
    const pageQuery = route?.query[QUERY_PARAM_KEY.PAGE];
    const currentPage  = pageQuery ? Number(pageQuery) : 0;
    if (currentPage > 1) {
      router.push({
        query: {
          ...route.query,
          [QUERY_PARAM_KEY.PAGE]: currentPage > 2 ? pageQuery - 1 : undefined,
        }
      })?.catch();
    }
  };

  const checkIfNeedSmoothUpdateByStates = (data, statesArray) => {
    const states = statesArray || smoothUpdateStates;
    const newState = data?.some((item) => states.includes(item.state)) || false;
    if (isSmoothUpdateNeeds.value !== newState) {
      isSmoothUpdateNeeds.value = newState;
    }
  };

  const runSmoothUpdate = () => {
    if (!smoothUpdate) {
      return;
    }

    const callSmoothUpdate = async () => {
      if (isSmoothUpdateNeeds.value) {
        await proxyFetch({ isSmoothUpdate: true });
      }
    };
    smoothTimer.value = setInterval(() => callSmoothUpdate(), smoothUpdateInterval);
  };

  const reset = ({ emitQueryParam = true } = {}) => {
    isDecrementPageLogicEnabled.value = false;
    tms_reset();
  };

  const toggleBodyClass = (isRemove = false) => {
    if (config.value.IS_CLIENT) {
      const tmClass = "iq-table-manager";
      const storeClass = `iq-table-manager-store-${storeId}`;
      if (!isRemove) {
        document?.body?.classList.add(tmClass, storeClass);
      } else {
        document?.body?.classList.remove(tmClass, storeClass);
      }
    }
  };

  onMounted(async () => {
    reset({ emitQueryParam: false });
    isDecrementPageLogicEnabled.value = true;

    if (immediate) {
      readyProject(async ({ isProjectReady }) => {
        if (isProjectReady) {
          await proxyFetch({});
          runSmoothUpdate();
        }
      });
    }

    toggleBodyClass();
  });

  onBeforeUnmount(() => {
    setSearchQuery("", { emitQueryParam: false });

    if (smoothTimer.value) {
      clearInterval(smoothTimer.value);
      smoothTimer.value = null;
    }

    toggleBodyClass(true);
  });

  watch(() => route.query, async () => {
    await proxyFetch({});
  });

  function _getParamFrom () {
    const pageQuery = route?.query[QUERY_PARAM_KEY.PAGE];
    return pageQuery ? (Number(pageQuery) - 1) * defaultPagination.size : defaultPagination.from;
  }

  return {
    tm_state: tms_state,
    tm_rows: tms_rows,
    tm_pagination: tms_pagination,
    tm_isLoading: tms_isLoading,
    tm_isFirstLoadCompleted: tms_isFirstLoadCompleted,
    tm_lang: lang,

    tm_setState: tms_setState,
    tm_setRows: tms_setRows,
    tm_setLoading:  tms_setLoading,
    tm_setPaginationFrom: tms_setPaginationFrom,
    tm_setPaginationTotal: tms_setPaginationTotal,
    tm_reset: reset,
    tm_fetch: proxyFetch,
    tm_getParams: getParams,
    tm_checkIfNeedSmoothUpdateByStates: checkIfNeedSmoothUpdateByStates,
    tm_runSmoothUpdate: runSmoothUpdate,
  };
}
