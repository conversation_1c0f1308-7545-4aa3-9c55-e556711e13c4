<template>
  <client-only>
    <content-wrapper :is-body-loading="tm_state === TABLE_MANAGER_STATE.LOADING">
      <template v-slot:title> <span>{{ $t('TAG.TAG_TEXT') }}</span> </template>

      <template v-slot:head>
        <button
          v-if="!isSearchEnabled"
          type="button"
          class="btn-green"
          @click="routeToTags()"
          @keydown="preventEnterAndSpaceKeyPress($event)"
        >
          {{ $t('TAG.NEW_TAG') }}
        </button>
      </template>

      <block-wrapper is-transparent>
        <template v-if="isSearchEnabled" v-slot:title>
          <div class="search-result-back-btn">
            <button
              type="button"
              class="btn-green-text btn-small padding-zero"
              @click="backToTag()"
            >
              <img src="@/assets/images/back-arrow.png" alt="Go back" aria-hidden="true" />
              <span>{{ $t('TAG.BACK_TO_TAGS') }}</span>
            </button>
            {{ $t('COMMON.SEARCH_RESULT') }}
          </div>
        </template>

        <simple-table
          v-if="tm_state === TABLE_MANAGER_STATE.DATA"
          :column-names="columnNames"
          :column-keys="columnKeys"
          :data-source="tm_rows"
          table-class="tags-table"
        >
          <template v-slot:isin="props">
            <span>{{ props.data?.isin }}</span>
          </template>

          <template v-slot:title="props">
            <span class="text-14 font-bold">{{ props.data?.name }}</span>
            <languages-alert
              :languages="props.data?.langs"
              :has-alert="props.data?.hasAlert"
              :lang="tm_lang"
              :alert-tooltip-text="$t('TAG.LANG_ALERT')"
            ></languages-alert>
          </template>

          <template v-slot:count="props">
            <p class="color-gray">
              {{ props.data?.totalRecipes }}
              <span v-if="props.data?.totalRecipes > 1">Recipes</span>
              <span v-else>Recipe</span>
            </p>
          </template>

          <template v-slot:status="props">
            <badge
              :label="$t(STATE_MAPPING[props.data?.state].tKey)"
              :badge-type="STATE_MAPPING[props.data?.state].badgeType"
              :img-src="STATE_MAPPING[props.data?.state].icon"
            ></badge>
          </template>

          <template v-slot:actions="props">
            <simple-actions
              :is-edit-btn-disabled="isActionDisabled(props.data?.state)"
              :is-edit-info-tooltip-showing="isActionDisabled(props.data?.state)"
              @editOnClick="ctaNavigate(props.data)"
              :is-delete-btn-disabled="isActionDisabled(props.data?.state) || props.data?.totalRecipes !== 0"
              :is-delete-info-tooltip-showing="isActionDisabled(props.data?.state)"
              :delete-btn-warn-tooltip-text="$t('TAG.SIMPLE_ACTIONS_DELETE_TOOLTIP')"
              @deleteOnClick="displayDeletePopUp(props.data)"
            ></simple-actions>
          </template>
        </simple-table>

        <simple-paginate
          :is-pagination-enabled="isPaginationShowing"
          :pagination-total="tm_pagination.total"
          :pagination-size="tm_pagination.size"
        />

        <noResultFound
          v-if="isNoResultFoundShowing"
          :isReloadRequired="reloadTextCheck"
          :isContentSearched="isSearchEnabled"
          :noResultText="$t('NO_RESULT.TAG')"
        ></noResultFound>
      </block-wrapper>
    </content-wrapper>
  </client-only>
</template>
<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useNuxtApp } from '#app';
import noResultFound from "@/components/no-result-found";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import BlockWrapper from "@/components/block-wrapper/block-wrapper.vue";
import SimpleTable from "@/components/simple-table/simple-table.vue";
import LanguagesAlert from "@/components/languages-alert.vue";
import Badge from "@/components/badge/badge.vue";
import { STATE_MAPPING } from "@/сonstants/state-mapping";
import SimpleActions from "@/components/simple-actions/simple-actions.vue";
import { useI18n } from 'vue-i18n';
import { useTableManager } from "../composables/useTableManager.js";
import SimplePaginate from "../components/simple-paginate.vue";
import { useQueryUtils } from "../composables/useQueryUtils.js";
import { useSearchStore } from "../stores/search.js";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";
import ConfirmModal from "../components/modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../models/confirm-modal.model.js";
import ProcessModal from "../components/modals/process-modal.vue";
import { PROCESS_MODAL_TYPE } from "../models/process-modal.model.js";
import { TABLE_MANAGER_STATE } from "../models/table-manager.model.js";

const { $tracker, $keys } = useNuxtApp();
const store = useStore();
const router = useRouter();
const { readyProject } = useProjectLang();
const { t } = useI18n();
const { preventEnterAndSpaceKeyPress } = useEventUtils();
const { getPageQuery, getSearchQuery } = useQueryUtils();
const { triggerLoading } = useCommonUtils();
const searchStore = useSearchStore();
const isSearchEnabled = searchStore.getIsSearchEnabled('global');
const {
  tm_state,
  tm_rows,
  tm_pagination,
  tm_lang,
  tm_fetch,
} = useTableManager({
  storeId: "tags",
  clientKey: "flite",
  endpointKey: "getTagMasterData",
  defaultParams: {
    categoryAlert: true,
    recipeTotal: true,
    includeAlerts: true,
  },
  smoothUpdate: true,
});

const { openModal, closeModal } = useBaseModal({
  "deleteModal": {
    component: ConfirmModal,
    skipClickOutside: true,
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      title: t('DESCRIPTION_POPUP.DELETE_TAG'),
      description: `${t('DESCRIPTION_POPUP.DELETE_POPUP')} ${t('DESCRIPTION_POPUP.TAG')}`,
    },
  },
  "deletingModal": {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
    props: {
      modalType: PROCESS_MODAL_TYPE.DELETING,
    },
  },
});

const reloadTextCheck = ref(false);
const deleteTagDetails = ref('');
const timer = ref(null);
const columnNames = ref([
  t('TAG.TAG_ISIN'),
  t('TAG.TAG_TITLE'),
  t('COMMON.RECIPE_COUNT'),
  t('COMMON.STATUS'),
  ""
]);
const columnKeys = ref(['isin', 'title', 'count', 'status', 'actions']);

const isNoResultFoundShowing = computed(() => (tm_state.value === TABLE_MANAGER_STATE.NO_DATA || tm_state === TABLE_MANAGER_STATE.EMPTY || tm_state === TABLE_MANAGER_STATE.ERROR));
const isPaginationShowing = computed(() => !!((tm_pagination.value.total > tm_pagination.value.size) && tm_rows.value.length));

const isActionDisabled = (state) => [$keys.STATE.PUBLISHING, $keys.STATE.UNPUBLISHING].includes(state);

const displayDeletePopUp = (tag) => {
  if (!isActionDisabled(tag.state)) {
    openModal({
      name: "deleteModal",
      onClose: (result) => result && postTagListAsync(tag.isin),
    });
  }
};

const backToTag = () => {
  searchStore.setSearchQuery('', { emitQueryParam: true, context: 'global' });
};

const postTagListAsync = async (isin) => {
  openModal({ name: "deletingModal" });
  const payload = {
    sourceId: 210030,
    data: {
      action: "removeAll",
      entityType: "recipe",
      isin,
    },
  };
  try {
    const response = await store.dispatch("categories/postCategoryRecipeAsync", { payload });
    await checkOperationStatusAsync(response.opId, isin);
  } catch {
    reloadTextCheck.value = true;
    closeModal("deletingModal");
    triggerLoading($keys.KEY_NAMES.SOMETHING_WENT_WRONG);
  }
};

const checkOperationStatusAsync = async (operationId, isin) => {
  const states = [$keys.KEY_NAMES.DONE, $keys.KEY_NAMES.FAILED];
  await getOperationStatusAsync(operationId);
  if (states.includes(deleteTagDetails.value.state)) {
    await deleteTagListAsync(isin);
    await tm_fetch({});
  } else {
    // Recheck the status if the operation is still ongoing
    timer.value = setTimeout(() => checkOperationStatusAsync(operationId, isin), 2000);
  }
};

const getOperationStatusAsync = async (operationId) => {
  try {
    await store.dispatch("categories/getOperationStatusAsync", { operationId });
    deleteTagDetails.value = store.getters["categories/getOperationStatus"];
  } catch (e) {
    console.error(e);
  }
}

const deleteTagListAsync = async (isin) => {
  const eventKey = await store.dispatch("categories/deleteCategoryList", { isin })
    .then(() => $keys.KEY_NAMES.NEW_DELETED_SUCCESS)
    .catch(() => $keys.KEY_NAMES.SOMETHING_WENT_WRONG);

  triggerLoading(eventKey);
  closeModal("deletingModal");
};

const ctaNavigate = (tag) => {
  if (!isActionDisabled(tag.state)) {
    router.push({
      path: "/edit-tag",
      query: {
        [QUERY_PARAM_KEY.BACK_FROM]: getPageQuery(),
        [QUERY_PARAM_KEY.ISIN]: tag.isin || undefined,
        [QUERY_PARAM_KEY.SEARCH]: getSearchQuery(),
      },
    });
  }
};

const routeToTags = () => router.push("/add-tag");
const resetStore = () => store.dispatch("tagData/resetTagList");

onMounted(() => {
  resetStore();
  readyProject(({ isProjectReady }) => {
    if (isProjectReady) {
      $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_TAGS, {}, { ...LOCAL_TRACKER_CONFIG });
    }
  });
});

onBeforeUnmount(() => {
  resetStore();
  if (timer.value) {
    clearTimeout(timer.value);
  }
});
</script>
