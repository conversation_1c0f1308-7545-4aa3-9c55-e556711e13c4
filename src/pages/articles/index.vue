<template>
  <client-only>
    <content-wrapper :is-body-loading="isLoading" body-classes="articles-body main-inner-section font-family-averta">
      <template v-slot:title>
        <span>{{ $t('ARTICLE.ARTICLE_MASTER_LIST') }}</span>
      </template>

      <template v-slot:head>
        <button
          type="button"
          class="btn articles-expansion-panel-toggle-btn"
          :class="{
            '__expanded': isAllExpansionPanelExpanded,
          }"
          @click="collapseAllExpansionPanels()"
        >
          <span>{{ isAllExpansionPanelExpanded ? $t('COMMON.COLLAPSE_ALL') : $t('COMMON.EXPAND_ALL') }}</span>
          <img alt="collapse icon" src="@/assets/images/arrow-down-green.png" />
        </button>


        <template v-if="!wasChangesRef">
          <button
            type="button"
            class="btn-green"
            @click="goToAddArticlePage()"
          >{{ $t('BUTTONS.NEW_ARTICLE') }}</button>
        </template>
        <template v-else>
          <button
            type="button"
            class="btn-green-outline"
            style="margin-right: 10px"
            @click="openCancelModal()"
          >{{ $t('BUTTONS.CANCEL_BUTTON') }}</button>

          <button
            type="button"
            class="btn-green"
            @click="openSaveModal()"
          >{{ $t('BUTTONS.SAVE_BUTTON') }}</button>
        </template>
      </template>

      <draggable
        v-if="isArticles"
        :list="articlesList"
        class="article-list-group"
        ghost-class="article-hidden-list"
        :scroll-sensitivity="200"
        :force-fallback="true"
        @start="drag = true"
        @end="drag = false"
        @update="draggableUpdate()"
        handle=".expansion-panel-drag"
      >
        <template v-for="(article, groupIndex) in articlesList">
          <expansion-panel
            :expanded="expandAllRef"
            :collapsedBtnViewLabel="$t('ARTICLE.VIEW_ARTICLES')"
            :collapsedBtnHideLabel="$t('ARTICLE.HIDE_ARTICLES')"
            :hideCollapsedBtn="!article.content?.length"
            @update="expansionPanelUpdate"
          >
            <template v-slot:head>
              <articles-list-head
                :article-id="article.uuid"
                :title="(groupIndex + 1) + '. ' + article.name"
                :description="article.content?.length + ' Articles'"
                :hideDescription="!article.content?.length"
                @edit-article="openEditCategoryModal"
                @delete-article="openDeleteCategoriesMasterArticlesModal"
              ></articles-list-head>
            </template>
            <template v-slot:body>
              <articles-list-content-table
                :article-id="article.uuid"
                :content-list="article.content || []"
                @draggable-update="draggableUpdate()"
                @call-action="callAction"
              ></articles-list-content-table>
            </template>
          </expansion-panel>
        </template>
      </draggable>

      <noResultFound
        v-if="!isArticles"
        :isReloadRequired="false"
        :noResultText="$t('NO_RESULT.ARTICLE')"
      ></noResultFound>

    </content-wrapper>
  </client-only>
</template>

<script setup>
import { useNuxtApp } from 'nuxt/app';
import { computed, onMounted, ref, reactive, watch } from "vue";
import { useArticlesStore } from "../../stores/articles.js";
import ArticlesListHead from "../../components/pages/articles/articles-list-head.vue";
import ArticlesListContentTable from "../../components/pages/articles/articles-list-content-table.vue";
import { useSimpleCustomFetch } from "../../composables/useCustomFetch.js";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { useProjectLang } from "../../composables/useProjectLang.js";
import { useDynamicHeroStore } from "../../stores/dynamic-hero.js";
import ArticleNewCategoryModal from "../../components/pages/articles/article-new-category-modal.vue";
import { useArticleItemStore } from "../../stores/article-item.js";
import ArticleItemPreviewModal from "../../components/pages/articles/article-item-preview-modal.vue";
import { useI18n } from "vue-i18n";
import ConfirmModal from "../../components/modals/confirm-modal.vue";
import ProcessModal from "../../components/modals/process-modal.vue";
import { PROCESS_MODAL_TYPE } from "../../models/process-modal.model.js";
import { CONFIRM_MODAL_TYPE } from "../../models/confirm-modal.model.js";

const { $keys, $t } = useNuxtApp();
const router = useRouter();
const store = useStore();
const { t } = useI18n();
const {
  articlesList,
  getArticlesAsync,
  getArticlesCategoriesAsync,
} = useArticlesStore();
const { setArticleDetailsForPreview, resetStore } = useArticleItemStore();
const { triggerLoading } = useCommonUtils();
const { delay } = useDelayTimer();
const { readyProject } = useProjectLang();
const { getDynamicHeroListDataAsync } = useDynamicHeroStore();
const { openModal, closeModal } = useBaseModal({
  "ArticlesPageArticleNewCategoryModal": ArticleNewCategoryModal,
  "ArticlesPageArticleItemPreviewModal": {
    component: ArticleItemPreviewModal,
    hideCloseBtn: false,
  },
  "ArticlesPageConfirmModal": ConfirmModal,
  "ArticlesPageProcessModal": {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
  },
});
const expandAllRef = ref(false);
const wasChangesRef = ref(false);
const isLoading = ref(true);
const updatedState = ref();
const selectedArticleForPreview = ref(null);

const expansionPanelList = reactive([]);

const isAllExpansionPanelExpanded = computed(() => {
  return !expansionPanelList?.length ? false : !expansionPanelList.find((item) => !item?.expanded)
});
const isArticles = computed(() => !!articlesList?.value?.length);

const collapseAllExpansionPanels = () => expandAllRef.value = !expandAllRef.value;
const expansionPanelUpdate = ({ id, expanded }) => {
  const itemIndex = expansionPanelList.findIndex((item) => item?.id === id);

  if (itemIndex !== -1) {
    expansionPanelList[itemIndex].expanded = expanded;
    return;
  }

  expansionPanelList.push({ id, expanded });
};

const draggableUpdate = () => wasChangesRef.value = true;

const openCancelModal = () => {
  openModal({
    name: "ArticlesPageConfirmModal",
    props: {
      modalType: CONFIRM_MODAL_TYPE.EXIT,
      title: t('DESCRIPTION_POPUP.EXIT_PAGE_POPUP'),
    },
    onClose: (response) => {
      if (response) {
        getArticlesAsync();
        wasChangesRef.value = false;
      }
    },
  });
};
const openSaveModal = () => {
  openModal({
    name: "ArticlesPageConfirmModal",
    props: {
      modalType: CONFIRM_MODAL_TYPE.SAVE,
      title: t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP'),
    },
    onClose: (response) => response && saveChanges(),
  });
};

const openDeleteCategoriesMasterArticlesModal = (uuid) => {
  openModal({
    name: "ArticlesPageConfirmModal",
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      title: "Delete the category?",
      description: `Are you sure you want to delete this category with all articles in this ${t('DESCRIPTION_POPUP.CATEGORY')}`,
    },
    onClose: (response) => response && deleteArticleCategoryAsync(uuid),
  });
};

const openEditCategoryModal = (uuid) => {
  getArticlesCategoriesAsync();
  const data = articlesList.value?.find((item) => item.uuid === uuid);
  openModal({
    name: "ArticlesPageArticleNewCategoryModal",
    props: {
      isEdit: true,
      categoryName: data.name,
      categoryOrder: data.order,
      categoryUUID: uuid,
    },
  });
};

const callAction = (data) => {
  const { key, uuid, isArticleIncludedInHero } = data;
  switch (key) {
    case "preview":
      prepareArticlePreview(uuid);
      openModal({ name: "ArticlesPageArticleItemPreviewModal" });
      break;
    case "unpublish":
      updatedState.value = $t('ARTICLE.UNPUBLISHED');
      prepareArticlePreview(uuid);
      openModal({
        name: "ArticlesPageConfirmModal",
        props: {
          modalType: CONFIRM_MODAL_TYPE.UNPUBLISH,
          title: t('ARTICLE.ARTICLE_UNPUBLISH_TEXT'),
          confirmBtnLabel: t('BUTTONS.UNPUBLISH_BUTTON')
        },
        onClose: (response) => response && patchArticlesDataAsync(),
      });
      break;
    case "publish":
      updatedState.value = $t('ARTICLE.PUBLISHED');
      prepareArticlePreview(uuid);
      openModal({
        name: "ArticlesPageArticleItemPreviewModal",
        props: {
          modalTitle: t('ARTICLE.ARTICLE_PUBLISH_CONFIRM_TEXT'),
          isActionsEnabled: true,
          confirmBtnLabel: t('BUTTONS.PUBLISH_BUTTON'),
        },
        onClose: (response) => response && patchArticlesDataAsync(),
      });
      break;
    case "edit":
      router.push({
        path: `/articles/${uuid}`,
        query: {
          isArticleIncludedInHero: isArticleIncludedInHero || undefined,
        },
      });
      break;
    case "delete":
      openModal({
        name: "ArticlesPageConfirmModal",
        props: {
          modalType: CONFIRM_MODAL_TYPE.DELETE,
          title: t('ARTICLE.ARTICLE_DELETE_TEXT'),
          description: `${t('DESCRIPTION_POPUP.DELETE_POPUP')} article?`,
        },
        onClose: (response) => response && deleteArticleAsync(uuid),
      });
  }
};

const prepareArticlePreview = (articleUUID) => {
  for (const category of articlesList.value) {
    const article = category.content?.find((el) => el.uuid === articleUUID);
    if (article) {
      selectedArticleForPreview.value = article;
      setArticleDetailsForPreview({
        title: article.title,
        image: article.image,
        category: category.name,
        pages: article.pages,
      });
      return;
    }
  }
};

const patchArticlesDataAsync = async () => {
  openModal({
    name: "ArticlesPageProcessModal",
    props: {
      modalType: updatedState.value === $t('ARTICLE.PUBLISHED') ? PROCESS_MODAL_TYPE.PUBLISHING : PROCESS_MODAL_TYPE.UNPUBLISHING,
    },
  });

  const article = selectedArticleForPreview.value;
  const payload = {
    title: article.title ?? "",
    order: article.order ?? "",
    image: article.image ?? "",
    source: article.source ?? "",
    lang: article.lang ?? "",
    pages: article.pages ?? [],
    isNew: article.isNew ?? false,
    hasVideo: article.hasVideo ?? false,
    category: article.category ?? "",
    state: updatedState.value,
  };
  const lang = store.getters["userData/getDefaultLang"];
  try {
    await useSimpleCustomFetch("", {
      method: "PATCH",
      body: payload,
      params: {
        lang,
      },
    }, "flite", "articleData", article.uuid);
    const messageKey = updatedState.value === $t('ARTICLE.PUBLISHED') ? $keys.KEY_NAMES.ARTICLE_PUBLISHED_SUCCESS : $keys.KEY_NAMES.ARTICLE_UNPUBLISHED_SUCCESS;
    triggerLoading(messageKey);
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "patchArticlesDataAsync:" , error);
    triggerLoading($keys.KEY_NAMES.ARTICLE_WRONG);
  } finally {
    closeModal("ArticlesPageProcessModal");
  }
  await getArticlesAsync();
};

const deleteArticleAsync = async (uuid) => {
  openModal({
    name: "ArticlesPageProcessModal",
    props: { modalType: PROCESS_MODAL_TYPE.DELETING }
  });
  const lang = store.getters["userData/getDefaultLang"];
  try {
    await useSimpleCustomFetch("", {
      method: "DELETE",
      params: {
        lang,
      },
    }, "flite", "articleData", uuid);
    triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
  } catch (error) {
    triggerLoading($keys.KEY_NAMES.ARTICLE_WRONG);
    console.error($keys.KEY_NAMES.ERROR_IN + "deleteArticleMasterDataAsync:" , error);
  } finally {
    closeModal("ArticlesPageProcessModal");
  }
  await getArticlesAsync();
};

const saveChanges = async () => {
  openModal({
    name: "ArticlesPageProcessModal",
    props: { modalType: PROCESS_MODAL_TYPE.SAVING }
  });
  const lang = store.getters["userData/getDefaultLang"];
  const orderArticlesList = articlesList.value.map((item, index) => ({
    uuid: item.uuid,
    order: index + 1,
  }));
  const orderArticlesContentList = articlesList.value
    .flatMap((item) => {
      if (item?.content?.length > 1) {
        return item?.content?.map((el, index) => ({
          uuid: el.uuid,
          order: index + 1,
        }));
      }

      return [];
    });

  try {
    await useSimpleCustomFetch("", {
      method: "PATCH",
      body: orderArticlesContentList,
      params: {
        lang,
      },
    }, "flite", "articleDataOrder");

    await useSimpleCustomFetch("", {
      method: "PATCH",
      body: orderArticlesList,
      params: {
        lang,
      },
    }, "flite", "articleCategoriesDataOrder");

    triggerLoading($keys.KEY_NAMES.SAVED_SUCCESS)
  } catch (error) {
    console.error("Cannot change articles order!", error);
    triggerLoading($keys.KEY_NAMES.ARTICLE_WRONG);
  } finally {
    closeModal("ArticlesPageProcessModal");
  }

  wasChangesRef.value = false;
  await getArticlesAsync();
};

const deleteArticleCategoryAsync = async (uuid) => {
  openModal({
    name: "ArticlesPageProcessModal",
    props: { modalType: PROCESS_MODAL_TYPE.DELETING }
  });
  try {
    await useSimpleCustomFetch("", { method: "DELETE" }, "flite", "articleCategoriesData", uuid);
    triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "deleteArticleCategoriesDataAsync:" , error);
    triggerLoading($keys.KEY_NAMES.ARTICLE_WRONG);
  } finally {
    closeModal("ArticlesPageProcessModal");
  }

  await getArticlesAsync();
};

const goToAddArticlePage = () => navigateTo("/articles/create");

watch(wasChangesRef, (newValue) => {
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, newValue);
});

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      const lang = store.getters["userData/getDefaultLang"];
      await getDynamicHeroListDataAsync({ lang });
      await getArticlesAsync({ lang });
      isLoading.value = false;
    }
  });
});

onBeforeUnmount(() => {
  resetStore();
});
</script>
