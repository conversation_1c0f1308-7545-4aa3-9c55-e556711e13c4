<template>
  <client-only>
    <content-wrapper class="padding-zero">
      <div class="top-section">
        <div class="back-button-container">
          <div @click="backToTagBtn()" class="tag-back-button">
            <img alt="" src="@/assets/images/back-arrow.png" />
            <p class="back-button-text text-title-2">
              {{ $t("TAG.BACK_TO_TAGS") }}
            </p>
          </div>
          <div class="tag-button-section">
            <button
              type="button"
              @click="backToTagBtn()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
              class="btn-green-outline"
            >
              {{ $t("BUTTONS.CANCEL_BUTTON") }}
            </button>
            <button
              type="button"
              @click="displayPopup()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
              :class="
                isCampaignModified &&
                tagName.trim() !== '' &&
                !isRecipeVariantNameEmpty
                  ? 'btn-green'
                  : 'disabled-button btn-green'
              "
            >
              {{ tagStatus == "active" ? "Publish" : "Save" }}
            </button>
          </div>
        </div>
      </div>
      <div class="tag-containers">
        <div class="tag-content-mains">
          <div class="tag-form-titles">
            <p class="tag-form-texts">Tag Form</p>
            <span class="right-section">
              <div class="publish-btn">
                <span class="text text-title-2">
                  {{ $t("COMMON.PUBLISH") }}
                </span>
                <label class="switch">
                  <input
                    type="checkbox"
                    :checked="tagStatus === 'active'"
                    @click.prevent="handleToggleClick"
                  />
                  <span class="slider-round"></span>
                </label>
              </div>
            </span>
          </div>
          <div
            class="add-title-container"
            :class="{
              'simple-data-tooltip': tagNameinfocus,
            }"
            :data-tooltip-text="tagNameinfocus && tagName"
          >
            <input
              type="text"
              id="tagName"
              autocomplete="off"
              class="add-title-text text-h2"
              v-model="tagName"
              placeholder="Add a title"
              @mouseover="checkTagName()"
              @keydown="hidetagTip()"
              @mouseleave="hidetagTip()"
              @input="hidetagTip()"
            />
            <img
              alt=""
              v-if="tagName == ''"
              class="compulsory-feild-tags"
              src="@/assets/images/asterisk.svg?skipsvgo=true"
            />
          </div>
          <div
            v-if="finalAvailableLangs && finalAvailableLangs.length > 1"
            class="tag-variant-section"
          >
            <div class="tag-variants-main">
              <div class="tag-variants">Tag Variants:</div>
              <div
                class="add-variant-section"
                :class="{
                  'simple-data-tooltip': recipeVariantLanguageList.length < 1,
                }"
                :data-tooltip-text="recipeVariantLanguageList.length < 1 && $t('COMMON.ADD_ONLY_ONE_VARIANT')"
              >
                <div
                  :class="
                    recipeVariantLanguageList.length > 0
                      ? 'add-variant-main'
                      : 'disable-add-variant-main add-variant-main'
                  "
                  @click="
                    recipeVariantLanguageList.length > 0
                      ? openTagVariantPopUp()
                      : ''
                  "
                >
                  <div class="add-variant-btn">
                    <img alt="" src="@/assets/images/category-add.png" />
                  </div>
                  <div class="add-variant-text text-h3">Add variant</div>
                </div>
              </div>
            </div>
            <div
              v-if="addTagVariantList.length <= 0"
              class="add-tag-variant text-title-2 font-normal"
            >
              Add tag variants to support multiple languages.
            </div>
            <div v-else class="tag-variant-card-main">
              <template v-for="(categoryVariant, index) in addTagVariantList">
                <variant-card-field
                  v-if="categoryVariant?.lang !== lang"
                  v-model="categoryVariant.name"
                  :prefix-label="displayLanguageCode(categoryVariant.lang)"
                  input-placeholder="Enter name"
                  @delete-action="deleteTagVariant(categoryVariant, index)"
                ></variant-card-field>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="add-recipe-main" v-if="recipeDataForTag.length == 0">
        <div class="add-recipe-content">
          <div class="left-section">
            <div class="head-text text-h2 font-normal">
              {{ $t("TAG.ADD_RECIPE_TO_TAG") }}
            </div>
            <div class="sub-text text-title-2 font-normal">
              {{ $t("TAG.ADD_RECIPES_TO_NEW_TAG") }}
            </div>
            <button
              type="button"
              class="btn-green"
              @click="addRecipe()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
            >
              {{ $t("COMMON.ADD_RECIPES") }}
            </button>
          </div>
          <div class="right-section">
            <div class="image-content">
              <img alt="" class="image" src="@/assets/images/pan-image.png" />
            </div>
          </div>
        </div>
      </div>
      <div class="tag-recipes-table-content" v-if="recipeDataForTag.length">
        <div class="content">
          <div class="recipe-tag">
            <div class="recipe-header-section">
              <div v-if="!isSearchExitEnable">
                <div
                  class="recipe-header text-h2"
                  v-if="recipeDataForTag.length <= 1"
                >
                  {{ recipeDataForTag.length }} {{ $t("TAG.RECIPE_IN_TAG") }}
                </div>
                <div
                  class="recipe-header text-h2"
                  v-if="recipeDataForTag.length > 1"
                >
                  {{ recipeDataForTag.length }} {{ $t("TAG.RECIPE_IN_TAGS") }}
                </div>
              </div>
              <div v-if="isSearchExitEnable">
                <div class="recipe-header text-h2">Search results</div>
              </div>
              <div v-if="!hasSelectionEnabled" class="search-section">
                <div
                  v-if="
                    !hasSelectionEnabled &&
                    !isSearchExitEnable &&
                    recipeDataForTag.length > 0
                  "
                  class="btn-green-text btn-small"
                >
                  <span @click="selectProducts()">{{
                    $t("COMMON.SELECT")
                  }}</span>
                </div>
                <div class="search-box">
                  <input
                    type="text"
                    class="search-input-box"
                    placeholder="Search for recipe name"
                    v-model.trim="queryRecipe"
                    autocomplete="off"
                    @keypress.enter="searchRecipeList()"
                    :class="{ 'align-search-input-box': queryRecipe }"
                  />
                  <img
                    alt=""
                    class="search-icon-green-image"
                    @click="searchRecipeList()"
                    src="@/assets/images/search-icon-green.png"
                  />
                  <img
                    alt=""
                    class="exit-search-icon"
                    v-if="isSearchExitEnable"
                    @click="resetQuery()"
                    src="@/assets/images/exit-gray.png"
                  />
                </div>
                <div class="add-btn" @click="addRecipe()">
                  <img
                    alt=""
                    class="add-image"
                    src="@/assets/images/category-add.png"
                  />
                  <span class="text text-h3">
                    {{ $t("COMMON.ADD_RECIPE") }}
                  </span>
                </div>
              </div>
            </div>
            <simple-sticky-wrapper
              v-if="hasSelectionEnabled"
              :top="70"
              :distance="-60"
              class="edit-selection-container"
            >
              <div class="edit-selection-panel">
                <div class="edit-select-all-checkbox-section">
                  <label
                    class="edit-checkbox-section checkbox"
                  >
                    <input
                      type="checkbox"
                      :checked="selectionOfRecipes[0].isSelected"
                      @click="selectAllMatches()"
                    />
                    <span class="checkmark"></span>
                  </label>
                </div>
                <button
                  type="button"
                  @click="selectAllMatches()"
                  class="btn-reset text-h3"
                >
                  {{ $t("PAGE.RECIPES.SELECT_ALL") }}
                </button>
                <div class="edit-selection">
                  <div class="edit-selected-text">
                    {{ checkSelectedRecipes }} {{ $t("COMMON.SELECTED") }}
                    <span
                      v-if="selectedProducts.length"
                      class="edit-selected-cross-icon"
                    >
                      <img
                        src="@/assets/images/close.svg?skipsvgo=true"
                        @click="removeAllSelected()"
                        alt="edit-close-icon"
                      />
                    </span>
                  </div>
                </div>
                <div class="edit-btn-container">
                  <button
                    type="button"
                    class="btn-red"
                    :disabled="selectedProducts.length == 0"
                    @click="deleteSelect()"
                  >
                    {{ $t("BUTTONS.REMOVE_BUTTON") }}
                  </button>
                  <button
                    type="button"
                    class="btn-green-text btn-small"
                    @click="cancelSelect()"
                  >
                    {{ $t("BUTTONS.CANCEL_BUTTON") }}
                  </button>
                </div>
              </div>
            </simple-sticky-wrapper>
            <div class="recipe-table-content">
              <div
                class="add-zero-section tag-recipe-section"
                v-if="recipeDataForTag.length == 0 && !isSearchExitEnable"
              >
                <div class="zero-promoted">
                  <span class="bold"> 0 RECIPE IN TAG. </span>
                  <span class="normal"> Add recipes in tag. </span>
                </div>
              </div>
              <div
                class="no-result-for-tag text-title-2"
                v-if="searchRecipesCount == 0 && isSearchExitEnable"
              >
                {{ $t("COMMON.NO_RESULT_FOUND") }}
              </div>
              <table
                class="recipe-table"
                id="recipeTable"
                v-if="recipeDataForTag"
              >
                <caption></caption>
                <thead
                  class="table-head"
                  v-if="recipeDataForTag.length != 0 && !queryText"
                >
                  <tr class="title">
                    <th scope="col"></th>
                    <th scope="col"></th>
                    <th class="category-group-isin" id="recipeIsinHeader">
                      <span>{{ $t("COMMON.RECIPE_ISIN") }} </span>
                    </th>
                    <th class="category-group-title" id="recipeIsinHeader">
                      <span>{{ $t("COMMON.RECIPE_TITLE") }}</span>
                    </th>
                    <th class="category-group-count" id="recipeIsinHeader">
                      <span>{{ $t("COMMON.TOTAL_TIME") }}</span>
                    </th>
                    <th scope="col">
                      <span>{{ $t("COMMON.INGREDIENT_COUNT") }}</span>
                    </th>
                    <th scope="col"></th>
                  </tr>
                </thead>
                <thead
                  class="table-head"
                  v-if="searchRecipesCount != 0 && queryText"
                >
                  <tr class="title">
                    <th scope="col"></th>
                    <th class="category-group-isin" id="recipeIsinHeader">
                      <span>{{ $t("COMMON.RECIPE_ISIN") }} </span>
                    </th>
                    <th class="category-group-title" id="recipeIsinHeader">
                      <span>{{ $t("COMMON.RECIPE_TITLE") }}</span>
                    </th>
                    <th class="category-group-count" id="recipeIsinHeader">
                      <span>{{ $t("COMMON.TOTAL_TIME") }}</span>
                    </th>
                    <th id="recipeIsinHeader">
                      <span>{{ $t("COMMON.INGREDIENT_COUNT") }}</span>
                    </th>
                    <th scope="col"></th>
                  </tr>
                </thead>
                <tbody
                  :style="{
                    cursor: hasSelectionEnabled ? 'pointer' : 'default',
                  }"
                >
                  <tr
                    class="body"
                    @click="selectMatchToDelete(index, recipe)"
                    :id="recipe.isdeleteSelected ? 'delete-selected' : ''"
                    v-for="(recipe, index) in recipeDataForTag"
                    :key="index"
                    v-show="!recipe.isSearched"
                    style="position: relative"
                    :class="{
                      'recipe-selected-color-category':
                        hasSelectionEnabled && recipe.isSelectedToDelete,
                    }"
                  >
                    <td>
                      <div
                        v-if="hasSelectionEnabled"
                        class="edit-product-table-checkbox"
                      >
                        <div
                          id="selectAllCheckboxId"
                          class="edit-select-all-checkbox-section"
                        >
                          <label class="edit-checkbox-section checkbox">
                            <input
                              @click="selectMatchToDelete(index, recipe)"
                              :checked="recipe.isSelectedToDelete"
                              type="checkbox"
                            />
                            <span class="checkmark"></span>
                          </label>
                        </div>
                      </div>
                    </td>
                    <td class="table-image-recipe">
                      <div class="image-recipe" v-if="recipe.media">
                        <img
                          alt=""
                          v-if="
                            recipe.media &&
                            recipe.media[lang] &&
                            recipe.media[lang].image &&
                            !recipe.media[lang].externalImageUrl
                          "
                          class="image"
                          :src="
                            recipe.media &&
                            recipe.media[lang] &&
                            recipe.media[lang].image
                              ? recipe.media[lang].image
                              : ''
                          "
                        />
                        <img
                          alt=""
                          v-if="
                            recipe.media &&
                            recipe.media[lang] &&
                            recipe.media[lang].externalImageUrl
                          "
                          class="image"
                          :src="
                            recipe.media &&
                            recipe.media[lang] &&
                            recipe.media[lang].externalImageUrl
                              ? recipe.media[lang].externalImageUrl
                              : ''
                          "
                          @error="$event.target.src = `${defaultImage}`"
                        />
                        <img
                          alt=""
                          v-if="
                            (!recipe.media ||
                              !recipe.media[lang] ||
                              !recipe.media[lang].image) &&
                            (!recipe.media ||
                              !recipe.media[lang] ||
                              !recipe.media[lang].externalImageUrl)
                          "
                          class="image"
                          :src="defaultImage"
                        />
                      </div>
                      <div class="image-recipe" v-if="recipe.image">
                        <img
                          alt=""
                          class="image"
                          :src="
                            recipe.image.url
                              ? recipe.image.url
                                ? recipe.image.url
                                : defaultImage
                              : ''
                          "
                        />
                      </div>
                      <div
                        class="image-recipe"
                        v-if="!recipe.image && !recipe.media"
                      >
                        <img alt="" class="image" :src="defaultImage" />
                      </div>
                    </td>
                    <td class="new-table-recipe-code">
                      <div class="recipe-code text-light-h4">
                        {{ recipe.isin ? recipe.isin : "" }}
                      </div>
                    </td>
                    <td class="add-tag-recipe-name">
                      <div class="recipes-title-wrapper">
                        <div
                          class="recipes-title font-size-14 font-bold color-black"
                          v-tooltip-if-overflow="recipe?.title[lang]"
                        >
                          <p class="simple-data-tooltip-text">{{ recipe?.title?.[lang] }}</p>
                        </div>
                        <div
                          v-if="recipe?.langs?.length > 1"
                          class="simple-data-tooltip text-light-h4"
                          :data-tooltip-text="getAvailableLanguagesTooltip()"
                        >
                          <img alt="globe" src="@/assets/images/language-icon.png" >
                        </div>
                      </div>
                      <div class="recipe-name text-h3" v-if="!recipe?.title?.[lang]">
                        {{ recipe?.title ?? "" }}
                      </div>
                      <div class="recipe-subtitle text-h3 font-normal" v-if="recipe?.subtitle?.[lang]">
                        <span>
                          {{ recipe?.subtitle?.[lang] }}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div class="recipe-details">
                        <div class="details text-h3 font-normal">
                          <span>
                            {{
                              recipe?.time?.total
                                ? parseDurationString(recipe.time.total) ?? ""
                                : $t("NONE")
                            }}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="recipe-details">
                        <div class="details text-h3 font-normal">
                          <span
                            v-if="
                              recipe.ingredients &&
                              recipe.ingredients[lang] &&
                              recipe.ingredients[lang].length == 1
                            "
                            >{{
                              recipe.ingredients && recipe.ingredients[lang]
                                ? recipe.ingredients[lang].length
                                : ""
                            }}
                            ingredient</span
                          >
                          <span
                            v-if="
                              recipe.ingredients &&
                              recipe.ingredients[lang] &&
                              recipe.ingredients[lang].length > 1
                            "
                            >{{
                              recipe.ingredients && recipe.ingredients[lang]
                                ? recipe.ingredients[lang].length
                                : ""
                            }}
                            ingredients</span
                          >
                        </div>
                      </div>
                    </td>
                    <td class="tag-published">
                      <div v-if="!hasSelectionEnabled" class="menu">
                        <div
                          :class="
                            recipe.dropDown
                              ? 'menu-container menu-selected'
                              : 'menu-container'
                          "
                          @click="displayOption(recipe)"
                        >
                          <img
                            alt=""
                            v-if="recipe.dropDown"
                            class="table-edit-btn"
                            src="@/assets/images/green-edit-btn.svg?skipsvgo=true"
                          />
                          <img
                            alt=""
                            v-if="!recipe.dropDown"
                            class="table-edit-btn"
                            src="@/assets/images/edit-btn.svg?skipsvgo=true"
                          />
                        </div>
                        <div class="menu-box" v-if="recipe.dropDown">
                          <ul class="menu-list text-title-2">
                            <li @click="editRecipe(recipe.isin)">
                              {{ $t("BUTTONS.PREVIEW_BUTTON") }}
                            </li>
                            <li
                              v-if="!hasSelectionEnabled"
                              @click="
                                openDeleteModal(recipe.isin, index),
                                  (recipe.dropDown = false)
                              "
                            >
                              {{ $t("BUTTONS.REMOVE_BUTTON") }}
                            </li>
                          </ul>
                        </div>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <deleteModal
        v-if="isRemoveAddTagVariantVisible"
        :closeModal="closeModal"
        :productInfoTitle="'Remove Tag Variant?'"
        :productDescriptionOne="'Are you sure you want to remove this variant from the'"
        :productDescriptionTwo="$t('DESCRIPTION_POPUP.TAG')"
        :deleteItem="removeTagVariant"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
      />
      <saveModal
        v-if="isSaveModalVisible && tagStatus != 'active'"
        :closeModal="closeModal"
        :saveAndPublishFunction="saveButtonClickAsync"
        :availableLang="[]"
        :buttonName="$t('BUTTONS.SAVE_BUTTON')"
        :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
        :imageName="'@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png'"
      />
      <saveModal
        v-if="isSaveModalVisible && tagStatus == 'active'"
        :closeModal="closeModal"
        :saveAndPublishFunction="saveButtonClickAsync"
        :availableLang="[]"
        :buttonName="'Publish'"
        :description="$t('DESCRIPTION_POPUP.PUBLISH_UPDATES_POPUP')"
        :imageName="'@/assets/images/publish-variant-icon.png'"
      />
      <addRecipeModal
        v-if="isAddRecipeModal"
        @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
        @closeModal="closeModal"
        :recipeDataForCategories="recipeDataForTag"
        :selectedCategoryRecipe="selectedTagRecipe"
        @campaignModifiedAddRecipe="campaignModifiedAddRecipe"
        :isAddTag="isAddTag"
      />
      <deleteModal
        v-if="isRemoveModalVisible"
        :closeModal="closeModal"
        :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPE')"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPE_POPUP')"
        :productDescriptionTwo="$t('DESCRIPTION_POPUP.TAG')"
        :deleteItem="deleteInstructionConfirm"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
      />
      <cancelModal
        v-if="isConfirmModalVisible"
        :availableLang="[]"
        :isCampaignModifiedFromShoppableReview="false"
        :callConfirm="backToTag"
        :closeModal="closeModal"
      />
      <Modal v-if="isPreviewRecipe" @close="closeModal">
        <template #noProductMatches>
          <div class="open-preview-recipe-modal">
            <div class="recipe-main-preview-header">
              <div class="recipe-preview-header">
                {{ $t("COMMON.RECIPE_PREVIEW") }}
              </div>
            </div>
            <img
              alt=""
              class="close-preview-recipe-modal"
              @click="closeModal"
              src="@/assets/images/exit-gray.png"
            />
            <div class="open-preview-recipe-main">
              <recipePreviewDetail
                :rISIN="recipeIsin"
                :checkRecipePreviewVideo="checkRecipePreviewVideo"
              ></recipePreviewDetail>
            </div>
          </div>
        </template>
      </Modal>
      <selectTheLanguageModal
        v-if="addTagVariantLanguagePopUp"
        :closeModal="closeModal"
        @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
        @nextVariantPopUp="nextTagVariantNameModalPopUp"
        @setRecipeVariantLanguageMatches="setTagVariantLanguageMatches"
        @showRecipeVariantLanguageMatches="showTagVariantLanguageMatches"
        :recipeVariantLanguageList="recipeVariantLanguageList"
        :hasRecipeVariantLanguageResult="addTagVariantLanguageResult"
      />
      <addVariant
        v-if="addTagVariantNamePopUp"
        :closeModal="closeModal"
        :typeName="'Tag'"
        :addVariantSelectedLanguage="addTagVariantSelectedLanguage"
        :itemName="tagName"
        @addConfirmVariant="addTagVariant"
        @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
        @backToRoute="backToSelectLanguageTagVariantPopUp"
      />
      <unpublishModal
        v-if="isUnPublishModalVisible"
        :description="'Do you want to unpublish this tag?'"
        :noteMessage="''"
        :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
        :unpublishFunction="unPublishConfirm"
        :closeModal="closeModal"
      />
      <saveModal
        v-if="isPublishModalVisible"
        :closeModal="closeModal"
        :saveAndPublishFunction="publishConfirm"
        :availableLang="[]"
        :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
        :description="$t('DESCRIPTION_POPUP.PUBLISH_POPUP')"
        :imageName="'@/assets/images/unpublished.png'"
      />
      <savingModal
        v-show="isTagPublishing"
        :status="tagStatus == 'active' ? 'publishing' : 'saving'"
      />
      <unableToContentModal
        v-if="isUnableToPublishArticle"
        :closeModal="closeModal"
        :text="$t('TEXT_POPUP.NOT_PUBLISHED')"
      />
      <deleteModal
        v-if="isSelectDeleteModalVisible"
        :closeModal="closeModal"
        :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPES')"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPES_POPUP')"
        :productDescriptionTwo="$t('DESCRIPTION_POPUP.TAG')"
        :deleteItem="deleteSelectProductMatches"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
      />
    </content-wrapper>
  </client-only>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watchEffect } from "vue";
import savingModal from "@/components/saving-modal";
import cancelModal from "@/components/cancel-modal";
import addVariant from "@/components/add-variant";
import unableToContentModal from "@/components/unable-to-content-modal";
import selectTheLanguageModal from "@/components/select-the-language";
import saveModal from "@/components/save-modal";
import deleteModal from "@/components/delete-modal";
import { useRouter, useRoute } from "vue-router";
import Modal from "@/components/Modal";
import defaultImage from "~/assets/images/default_recipe_image.png";
import recipePreviewDetail from "@/components/recipe-preview-detail";
import addRecipeModal from "@/components/add-recipe-modal.vue";
import unpublishModal from "@/components/unpublish-modal";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useDelayTimer } from "@/composables/useDelayTimer";
import { useEventUtils } from "@/composables/useEventUtils";
import { useTimeUtils } from "@/composables/useTimeUtils";
import { useCommonUtils } from "@/composables/useCommonUtils";
import { useRefUtils } from "~/composables/useRefUtils";
import { useNuxtApp } from "#app";
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
import SimpleStickyWrapper from "@/components/simple-sticky-wrapper.vue";

const store = useStore();
const router = useRouter();
const route = useRoute();
const { $tracker, $eventBus, $auth, $keys } = useNuxtApp();
const { readyProject } = useProjectLang();
const { getRef } = useRefUtils();
const { delay } = useDelayTimer();
const { parseDurationString } = useTimeUtils();
const isAddTag = ref(true);
const { t } = useI18n();
const { triggerLoading } = useCommonUtils();
const { preventEnterAndSpaceKeyPress, onEscapeKeyPress } = useEventUtils();
const isCheckVideoPreview = ref(false);
const isRemoveTagVariantVisible = ref(false);
const isTagPublishing = ref(false);
const isUnableToPublishArticle = ref(false);
const isRemoveModalVisible = ref(false);
const isAddRecipeModal = ref(false);
const isCampaignModified = ref(false);
const queryText = ref("");
const queryRecipe = ref("");
const isSearchExitEnable = ref(false);
const isPreviewRecipe = ref(false);
const recipeIsin = ref("");
const showLoader = ref(false);
const recipeDataForTag = ref([]);
const selectedTagRecipe = ref([]);
const newIsin = ref("");
const isSaveModalVisible = ref(false);
const tagName = ref("");
const operationStatusDetails = ref("");
const dropdownItem = ref([]);
const isConfirmModalVisible = ref(false);
const isUnPublishModalVisible = ref(false);
const isPublishModalVisible = ref(false);
const deleteRecipeTagISIN = ref("");
const addVariantTagNamePopUp = ref(false);
const addTagVariantLanguagePopUp = ref(false);
const recipeVariantLanguageList = ref([]);
const variantName = ref("");
const addTagVariantList = ref([]);
const searchRecipesCount = ref(0);
const indexOfRecipe = ref(0);
const tagStatus = ref("hidden");
const tagVariantDataIndex = ref("");
const addTagVariantLanguageResult = ref(false);
const recipeVariantLanguage = ref("");
const addTagVariantSelectedLanguage = ref("");
const addTagVariantNamePopUp = ref(false);
const isRemoveAddTagVariantVisible = ref(false);
const selectedDefaultLang = ref([]);
const deletedvariant = ref(false);
const addTagVariantLanguageIndex = ref(0);
const isRecipeVariantNameEmpty = ref(false);
const tagNameinfocus = ref(false);
const selectionOfRecipes = ref([{ isSelected: false }]);
const hasSelectionEnabled = ref(false);
const selectedProducts = ref([]);
const isSelectDeleteModalVisible = ref(false);
const isAdminCheck = ref(false);
const finalAvailableLangs = ref([]);
const lang = ref("");
const addTagVariantDataIndex = ref();

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      lang.value = store.getters["userData/getDefaultLang"];
      finalAvailableLangs.value = store.getters["userData/getAvailableLangs"];
      isAddTag.value = true;
      document.addEventListener("input", handleTypeInput);
      document.addEventListener("click", handleClickOutside);
      document.addEventListener("click", handleClickOutsidePopup);

      $eventBus.on("backButtonConfirm", () => {
        backToTagConfirm();
      });
      $eventBus.on("closeModal", () => {
        closeModal();
      });
      selectedDefaultLang.value.push(lang.value);
      if (finalAvailableLangs.value) {
        finalAvailableLangs.value.forEach((lang) => {
          let langData = {};
          if (lang === "es-US") {
            langData = {
              language: lang,
              language_name: "Spanish",
              languageFlag: "/images/flags/spain-flag.png",
            };
          } else if (lang === "fr-FR") {
            langData = {
              language: lang,
              language_name: "French",
              languageFlag: "/images/flags/france-flag.png",
            };
          }
          if (!selectedDefaultLang.value.includes(lang)) {
            recipeVariantLanguageList.value.push(langData);
          }
        });
      }
    }
  });
});

onBeforeUnmount(() => {
  document.removeEventListener("scroll", checkScrollPosition);
  document.removeEventListener("input", handleTypeInput);
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("click", handleClickOutsidePopup);
});

const getAvailableLanguagesTooltip = () => {
  if (!recipeDataForTag?.value?.length) return "";

  const uniqueLanguages = [
    ...new Set(
      recipeDataForTag.value.flatMap(recipe =>
        recipe.langs.map((item) => displayTooltipLanguage(item))
      )
    ),
  ];

  return `Available in ${uniqueLanguages.join(" ")}`;
};
const checkSelectedRecipes = computed(() => {
  return selectedProducts.value.filter((data) => data.isSelectedToDelete)
    .length;
});

const shouldPublishToggle = computed(() => {
  return (
    isCampaignModified.value &&
    tagName.value.length &&
    !isRecipeVariantNameEmpty.value
  );
});

const removeAllSelected = () => {
  recipeDataForTag.value.forEach((item) => {
    item.isSelectedToDelete = false;
  });
  selectionOfRecipes.value[0].isSelected = false;
  selectedProducts.value = [];
};

const handleToggleClick = () => {
  shouldPublishToggle.value ? publishToggleBtn() : publishToggleBtnPopup();
};

const selectAllMatches = () => {
  selectionOfRecipes.value[0].isSelected =
    !selectionOfRecipes.value[0].isSelected;
  if (selectionOfRecipes.value[0].isSelected) {
    recipeDataForTag.value.forEach((item) => {
      item.isSelectedToDelete = true;
      selectedProducts.value.push(item);
    });
  } else {
    recipeDataForTag.value.forEach((item) => {
      item.isSelectedToDelete = false;
      selectedProducts.value = selectedProducts.value.filter(
        (insideItem) => item.isin !== insideItem.isin
      );
    });
  }
  selectedProducts.value = selectedProducts.value.filter(
    (value, index, self) =>
      index === self.findIndex((t) => t.isin === value.isin)
  );
};

const selectMatchToDelete = (data, product) => {
  if (hasSelectionEnabled.value) {
    recipeDataForTag.value.forEach((item, index) => {
      if (index === data) {
        item.isSelectedToDelete = !item.isSelectedToDelete;
        if (item.isSelectedToDelete) {
          selectedProducts.value.push(item);
        } else {
          selectedProducts.value = selectedProducts.value.filter(
            (insideData) => insideData.isin !== product.isin
          );
        }
      }
    });
    checkSelected();
  }
};

const deleteSelectProductMatches = () => {
  const excludeIsin = selectedProducts.value
    .filter(product => product.isSelectedToDelete)
    .map(product => product.isin);

  recipeDataForTag.value = recipeDataForTag.value.filter(
    (data) => !excludeIsin.includes(data.isin)
  );
  selectedTagRecipe.value = selectedTagRecipe.value.filter(
    (data) => !excludeIsin.includes(data)
  );
  selectedProducts.value = [];
  selectionOfRecipes.value[0].isSelected = false;
  hasSelectionEnabled.value = false;
  isCampaignModified.value = true;
  closeModal();
  triggerLoading($keys.KEY_NAMES.DELETED);
};

const checkSelected = () => {
  const count = recipeDataForTag.value.filter(
    (item) => item.isSelectedToDelete
  ).length;
  selectionOfRecipes.value[0].isSelected =
    count === recipeDataForTag.value.length;
};

const cancelSelect = () => {
  hasSelectionEnabled.value = false;
  selectedProducts.value = [];
  recipeDataForTag.value.forEach((item) => {
    item.isSelectedToDelete = false;
  });
};

const selectProducts = () => {
  hasSelectionEnabled.value = true;
};
watchEffect(() => {
  if (isCampaignModified.value) {
    $eventBus.emit("campaignModified", isCampaignModified.value);
    checkVariantNameEmpty();
  }
});
const checkScrollPosition = () => {
  if (hasSelectionEnabled.value) {
    const ele = document.querySelector("#recipeTable");
    if (
      ele.getBoundingClientRect().top < 0 &&
      hasSelectionEnabled.value &&
      recipeDataForTag.value.length >= 4
    ) {
      changeSelectbarPosition();
    }
  }
};

const changeSelectbarPosition = () => {
  const ele = document.querySelector(".edit-category-selection-container");
  const deletebtn = document.querySelector(".edit-category-btn-container");
  const cancelbtn = document.querySelector(".edit-category-cancel-btn");
  const selectText = document.querySelector(".edit-category-selected-text");
  const selectAll = document.querySelector(".edit-category-select-all-text");
  const selectBox = getRef("selectAllCheckboxId");
  const selectionPanel = document.querySelector(
    ".edit-category-selection-panel"
  );

  if (hasSelectionEnabled.value) {
    ele.style.backgroundColor = "#FFFFFF";
    ele.style.height = "64px";
    ele.style.alignItems = "center";
    ele.style.paddingTop = "inherit";
    ele.style.position = "fixed";
    ele.style.zIndex = "999";
    ele.style.top = "60px";
    ele.style.width = "-webkit-fill-available";
    ele.style.marginLeft = "-20px";
    ele.style.boxShadow = "1px 1px 4px 0px #888888";
    deletebtn.style.right = "300px";
    cancelbtn.style.right = "95px";
    selectText.style.left = "161px";
    selectAll.style.left = "74px";
    selectBox.style.marginLeft = "29px";
    selectionPanel.style.marginTop = "15px";
  }
};

const deleteSelect = () => {
  if (selectedProducts.value.length > 0) {
    isSelectDeleteModalVisible.value = true;
  }
};

const checkTagName = () => {
  if (
    tagName.value &&
    tagName.value.scrollWidth > tagName.value.clientWidth &&
    tagName.value.trim().length
  ) {
    tagNameinfocus.value = true;
  }
};

const hidetagTip = () => {
  tagNameinfocus.value = false;
};

const campaignModifiedAddRecipe = () => {
  isCampaignModified.value = true;
};

const backToTag = () => {
  backToTagConfirm();
  closeModal();
};

const checkRecipePreviewVideo = (data) => {
  isCheckVideoPreview.value = !!data;
};

const checkVariantNameEmpty = () => {
  if (!addTagVariantList.value.length) {
    isRecipeVariantNameEmpty.value = false;
    return;
  }
  let count = 0;
  addTagVariantList.value.forEach((data) => {
    let variantNameValue = data.name;
    if (variantNameValue.trim().length === 0) {
      count++;
    }
  });
  isRecipeVariantNameEmpty.value = count > 0;
};

const displayTooltipLanguage = (item, index, langlength) => {
  const arr = item.split("-");
  if (item !== lang.value) {
    return index < langlength
      ? arr[0].toUpperCase() + ","
      : arr[0].toUpperCase() + ".";
  }
};

const displayLanguageCode = (item) => {
  if (item) {
    const arr = item.split("-");
    return arr[0].toUpperCase();
  }
  return "";
};

const addTagVariant = (item) => {
  variantName.value = item;
  if (variantName.value !== "") {
    const newVariantData = {
      name: item.trim(),
      lang: recipeVariantLanguage.value,
    };
    addTagVariantList.value.push(newVariantData);
    addTagVariantNamePopUp.value = false;
    variantName.value = "";
    isCampaignModified.value = true;
    recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter(
      (data) => data.language !== recipeVariantLanguage.value
    );
  }
};

const nextTagVariantNameModalPopUp = (item) => {
  if (item === "") {
    addTagVariantSelectedLanguage.value =
      recipeVariantLanguageList.value[0].language;
    recipeVariantLanguage.value = recipeVariantLanguageList.value[0].language;
  } else {
    addTagVariantSelectedLanguage.value = item;
  }
  addTagVariantLanguagePopUp.value = false;
  addTagVariantNamePopUp.value = true;
  addTagVariantLanguageResult.value = false;
};

const showTagVariantLanguageMatches = () => {
  addTagVariantLanguageResult.value = !addTagVariantLanguageResult.value;
};

const backToSelectLanguageTagVariantPopUp = () => {
  addTagVariantNamePopUp.value = false;
  addTagVariantLanguagePopUp.value = true;
};

const openTagVariantPopUp = () => {
  addTagVariantLanguagePopUp.value = true;
  addTagVariantLanguageResult.value = false;
  recipeVariantLanguage.value = "";
};

const setTagVariantLanguageMatches = (value, index) => {
  recipeVariantLanguage.value = value.language;
  addTagVariantLanguageIndex.value = index;
  addTagVariantLanguageResult.value = false;
  recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter(
    (data) => data.language !== recipeVariantLanguage.value
  );
  recipeVariantLanguageList.value.unshift(value);
};

const deleteTagVariant = (data, index) => {
  isRemoveAddTagVariantVisible.value = true;
  addTagVariantDataIndex.value = index;
  deletedvariant.value = data.lang;
};

const removeTagVariant = () => {
  addTagVariantList.value.splice(addTagVariantDataIndex.value, 1);
  let langData = {};
  if (deletedvariant.value === "es-US") {
    langData = {
      language: deletedvariant.value,
      language_name: "Spanish",
      languageFlag: "/images/flags/spain-flag.png",
    };
  } else if (deletedvariant.value === "fr-FR") {
    langData = {
      language: deletedvariant.value,
      language_name: "French",
      languageFlag: "/images/flags/france-flag.png",
    };
  }
  recipeVariantLanguageList.value.push(langData);
  isCampaignModified.value = true;
  closeModal();
};

const editRecipe = (isin) => {
  if (isin) {
    recipeIsin.value = isin;
    isPreviewRecipe.value = true;
  }
};

const handleTypeInput = (event) => {
  const tagNameRef = getRef("tagName");
  if (tagNameRef && tagNameRef.contains(event.target)) {
    isCampaignModified.value = true;
  }
  const tagGroupVariantInput = document.querySelector(
    ".input-for-tag-group-variant"
  );
  if (tagGroupVariantInput && tagGroupVariantInput.contains(event.target)) {
    isCampaignModified.value = true;
  }
};

const handleClickOutside = (event) => {
  if (dropdownItem.value && dropdownItem.value.dropDown) {
    const menuSelected = document.querySelector(".menu-selected");
    if (menuSelected && !menuSelected.contains(event.target)) {
      dropdownItem.value.dropDown = false;
    }
  }
};

const handleClickOutsidePopup = (event) => {
  if (addTagVariantLanguageResult.value) {
    const categoryGroupDropdown = document.querySelector(
      ".category-group-dropdown"
    );
    if (
      categoryGroupDropdown &&
      !categoryGroupDropdown.contains(event.target)
    ) {
      addTagVariantLanguageResult.value = false;
    }
  }
};

const publishToggleBtn = () => {
  if (tagStatus.value === "active") {
    isUnPublishModalVisible.value = true;
  }
  if (tagStatus.value === "hidden") {
    isPublishModalVisible.value = true;
  }
};

const publishToggleBtnPopup = () => {
  if (tagStatus.value !== "active") {
    isUnableToPublishArticle.value = true;
  } else {
    publishToggleBtn();
  }
};

const unPublishConfirm = () => {
  isCampaignModified.value = true;
  tagStatus.value = "hidden";
  closeModal();
};

const publishConfirm = () => {
  isCampaignModified.value = true;
  tagStatus.value = "active";
  closeModal();
};

const searchRecipeList = () => {
  resetSearch();
  if (queryRecipe.value !== "") {
    filterRecipes();
    isSearchExitEnable.value = true;
  } else {
    resetSearchFlags();
    isSearchExitEnable.value = false;
  }
};

const resetSearch = () => {
  searchRecipesCount.value = 0;
  queryText.value = queryRecipe.value;
  recipeDataForTag.value.forEach((data) => {
    data.isSearched = false;
  });
};

const filterRecipes = () => {
  const filter = queryRecipe.value.toUpperCase();

  recipeDataForTag.value.forEach((data) => {
    const isinValue = data.isin || "";
    const textValue = getTextValue(data).toUpperCase();
    data.isSearched = !(
      isinValue.includes(filter) || textValue.includes(filter)
    );
  });

  searchRecipesCount.value = recipeDataForTag.value.filter(
    (data) => !data.isSearched
  ).length;
};

const getTextValue = (data) => {
  return (
    data.title?.[lang.value]?.toUpperCase() ||
    data.title?.[0]?.toUpperCase() ||
    ""
  );
};

const resetSearchFlags = () => {
  recipeDataForTag.value.forEach((data) => {
    data.isSearched = false;
  });
};

const resetQuery = () => {
  queryRecipe.value = "";
  searchRecipesCount.value = 0;
  searchRecipeList();
};

const displayOption = (item) => {
  dropdownItem.value = item;
  item.dropDown = !item.dropDown;

  recipeDataForTag.value.forEach((data) => {
    if (item.isin !== data.isin) {
      data.dropDown = false;
    }
  });
};

const addRecipe = () => {
  resetQuery();
  isAddRecipeModal.value = true;
};

const backToTagConfirm = () => {
  isCampaignModified.value = false;
  router.push({
    path: "/tags",
  });
};

const backToTagBtn = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToTagConfirm();
  }
};

const closeModal = () => {
  isSelectDeleteModalVisible.value = false;
  isRemoveTagVariantVisible.value = false;
  isUnableToPublishArticle.value = false;
  isAddRecipeModal.value = false;
  isPreviewRecipe.value = false;
  isUnPublishModalVisible.value = false;
  isPublishModalVisible.value = false;
  isRemoveModalVisible.value = false;
  isSaveModalVisible.value = false;
  isConfirmModalVisible.value = false;
  addTagVariantLanguagePopUp.value = false;
  addVariantTagNamePopUp.value = false;
  isRemoveAddTagVariantVisible.value = false;
  variantName.value = "";
  addTagVariantNamePopUp.value = false;
};

const openDeleteModal = (deleteRecipe, index) => {
  isRemoveModalVisible.value = true;
  indexOfRecipe.value = index;
  deleteRecipeTagISIN.value = deleteRecipe;
};

const deleteInstructionConfirm = () => {
  isCampaignModified.value = true;
  isRemoveModalVisible.value = false;
  recipeDataForTag.value.splice(indexOfRecipe.value, 1);
  let removeRecipeTagIndex = selectedTagRecipe.value.indexOf(
      deleteRecipeTagISIN.value
    );
    if (selectedTagRecipe.value.includes(deleteRecipeTagISIN.value)) {
      selectedTagRecipe.value.splice(removeRecipeTagIndex, 1);
    }
    closeModal();
    indexOfRecipe.value = 0;
    deleteRecipeTagISIN.value = "";
    searchRecipesCount.value = searchRecipesCount.value - 1;
    $eventBus.emit($keys.KEY_NAMES.DELETED);
};
const displayPopup = () => {
  if (recipeDataForTag.value?.length) {
    recipeDataForTag.value.forEach((data) => {
      data.dropDown = false;
    });
  }
  isSaveModalVisible.value = true;
};

const patchPublishTagAsync = async () => {
  if (newIsin.value) {
    const payload = { status: tagStatus.value };
    try {
      await store.dispatch("categories/patchCategoryAsync", {
        payload,
        isin: newIsin.value,
      });
      isTagPublishing.value = false;
      const loadingStatus = tagStatus.value !== 'active' ? "savedSuccess" : "isPublishedData";
      triggerLoading(loadingStatus);
      router.push({ path: "/tags" });
    } catch (error) {
      isTagPublishing.value = false;
      console.error(error);
    }
  }
};

const pageRoute = () => {
  router.push({ path: "/tags" });
};

const postTagRecipeAsync = async () => {
  const payload = {
    sourceId: "210030",
    data: {
      action: "add",
      isin: newIsin.value,
      targets: selectedTagRecipe.value,
    },
  };
  try {
    const response = await store.dispatch(
      "categories/postCategoryRecipeAsync",
      { payload }
    );
    const operationId = response.opId;
    await checkOperationStatusAsync(operationId);
    await patchPublishTagAsync();
  } catch (error) {
    console.error(error);
    isTagPublishing.value = false;
  }
};

const checkTagEvent = (description) => {
  const eventProperties = {
    [t("EVENT_NAMES.TAG_NAME")]: tagName.value.trim(),
  };

  $tracker.sendEvent(description, eventProperties, { ...LOCAL_TRACKER_CONFIG });
};

const saveButtonClickAsync = async () => {
  isSaveModalVisible.value = false;
  isTagPublishing.value = true;
  await getTagIsinAsync();

  if (newIsin.value && tagName.value) {
    await postTagAsync();
  }
  isCampaignModified.value = false;
  $eventBus.emit("campaignModified", isCampaignModified.value);
};

const getTagIsinAsync = async () => {
  const payload = {
    entity: "recipeGroup",
  };
  try {
    await store.dispatch("isin/getNewISINsAsync", {
      payload,
      lang: lang.value,
    });
    const response = store.getters["isin/getISIN"];
    newIsin.value = response?.isin ?? "";
  } catch (error) {
    console.error(error);
    isTagPublishing.value = false;
  }
};

const updatedTagVariantList = (variantList) => {
  if (variantList && variantList.length > 0) {
    variantList.forEach((item) => {
      if (item && item.name !== "" && item.lang !== "") {
        item[item.lang] = { name: item.name || "" };
      }
    });
  }
  let updatedVariantList = Object.assign({}, ...variantList);
  delete updatedVariantList.lang;
  delete updatedVariantList.name;
  return updatedVariantList;
};

const postTagAsync = async () => {
  const defaultVariantData = {
    name: tagName.value.trim(),
    lang: lang.value,
  };
  addTagVariantList.value.push(defaultVariantData);
  const payload = {
    isin: newIsin.value,
    type: "tag",
    data: updatedTagVariantList(addTagVariantList.value),
  };
  try {
    await store.dispatch("categories/postCategoryOrCategoryGroupAsync", {
      payload,
      lang: lang.value,
    });
    if (selectedTagRecipe.value.length) {
      await postTagRecipeAsync();
    } else {
      await patchPublishTagAsync();
    }
    checkTagEvent($keys.EVENT_KEY_NAMES.CLICK_ADD_TAG);
  } catch (error) {
    console.error(error);
    showLoader.value = false;
    isTagPublishing.value = false;
    checkTagEvent($keys.EVENT_KEY_NAMES.VIEW_TAG_ERROR);
  }
};

const checkOperationStatusAsync = async (operationId) => {
  while (true) {
    await getOperationStatusAsync(operationId);
    if (
      operationStatusDetails.value.state === "done" ||
      operationStatusDetails.value.state === "failed"
    ) {
      operationStatusDetails.value = "";
      break;
    }
  }
};

const getOperationStatusAsync = async (operationId) => {
  try {
    await store.dispatch("categories/getOperationStatusAsync", { operationId });
    const response = store.getters["categories/getOperationStatus"];
    operationStatusDetails.value = response;
  } catch (error) {
    console.error(error);
  }
};
onEscapeKeyPress(closeModal);
</script>
