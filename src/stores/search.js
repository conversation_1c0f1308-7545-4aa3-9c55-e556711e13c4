export const useSearchStore = defineStore("Search", () => {
  const searchContexts = ref({
    global: {
      str: "",
      emitQueryParam: true,
    },
    'details-page': {
      str: "",
      emitQueryParam: false,
    }
  });

  const isSearchEnabledContexts = ref({
    global: false,
    'details-page': false
  });

  const getSearchQuery = (context = 'global') => {
    return computed(() => searchContexts.value[context] || { str: "", emitQueryParam: true });
  };

  const getIsSearchEnabled = (context = 'global') => {
    return computed(() => isSearchEnabledContexts.value[context] || false);
  };

  const setSearchQuery = (value, { emitQueryParam = true, context = 'global' } = {}) => {
    const trimmed = value?.trim() ?? '';

    if (!searchContexts.value[context]) {
      searchContexts.value[context] = { str: "", emitQueryParam: true };
    }
    if (!isSearchEnabledContexts.value[context]) {
      isSearchEnabledContexts.value[context] = false;
    }

    searchContexts.value[context] = {
      str: trimmed,
      emitQueryParam,
    };

    isSearchEnabledContexts.value[context] = !!trimmed;
  };

  const searchQuery = computed(() => searchContexts.value.global);
  const isSearchEnabled = computed(() => isSearchEnabledContexts.value.global);

  return {
    searchQuery,
    isSearchEnabled,
    setSearchQuery,
    getSearchQuery,
    getIsSearchEnabled,
  };
});
