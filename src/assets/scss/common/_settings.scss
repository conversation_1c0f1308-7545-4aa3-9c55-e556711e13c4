$line-height-base: 1.3;

$shell-max-width: 1224px;
$shell-gutter: 10px;

$responsive-xxs: 374px;
$responsive-xs: 767px;
$responsive-sm: 1023px;
$responsive-md: 1200px;

$breakpoint-desktop: '(min-width: #{$responsive-md})';
$breakpoint-mobile-portrait: '(max-width: #{$responsive-xxs})';
$breakpoint-mobile: '(max-width: #{$responsive-xs})';
$breakpoint-tablet-portrait: '(max-width: #{$responsive-sm})';
$breakpoint-small-desktop: '(max-width: #{$responsive-md})';
$retina: '(min-resolution: 2dppx)';

$border-radius: 8px;


// content-wrapper component
$content-wrapper-top-shift: 70px;
$content-wrapper-left-shift: 240px;

$content-wrapper-padding-top: 30px;
$content-wrapper-padding-left: 44px;
$content-wrapper-padding-right: 30px;
$content-wrapper-padding-bottom: 45px;
