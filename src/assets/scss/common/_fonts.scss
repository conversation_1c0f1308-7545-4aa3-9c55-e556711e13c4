@font-face {
  font-family: 'Averta';
  src: url('../fonts/Averta-Semibold.woff2') format('woff2'),
      url('../fonts/Averta-Semibold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Averta';
  src: url('../fonts/Averta-ExtraBold.woff2') format('woff2'),
      url('../fonts/Averta-ExtraBold.woff') format('woff');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Averta';
  src: url('../fonts/Averta-Regular.woff2') format('woff2'),
      url('../fonts/Averta-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Averta';
  src: url('../fonts/Averta-Thin.woff2') format('woff2'),
      url('../fonts/Averta-Thin.woff') format('woff');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'Averta';
  src: url('../fonts/Averta-Bold.woff2') format('woff2'),
      url('../fonts/Averta-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Averta';
  src: url('../fonts/Averta-Light.woff2') format('woff2'),
      url('../fonts/Averta-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Avenir';
  src: url('../fonts/Avenir-Book.woff') format('woff'),
      url('../fonts/Avenir-Book.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

// Font names
$font-family-averta: "Averta", Arial, Helvetica, sans-serif;
$font-family-serif: Times, "Times New Roman", serif;
$font-avenir: "Avenir", sans-serif;
$font-family-arial-serif: Arial, Helvetica, sans-serif;
$font-family-Helvetica: Helvetica, sans-serif;
$font-family-open-sans: "Open Sans", sans-serif;
$font-family-averta-regular: Averta-Regular, Arial, Helvetica, sans-serif;
$font-family-averta-bold: "Averta Bold", "Averta", Arial, Helvetica, sans-serif;

// Font sizes
$font-size-10: 10px;
$font-size-12: 12px;
$font-size-14: 14px;
$font-size-base: 16px;
$font-size-18: 18px;
$font-size-19: 19px;
$font-size-20: 20px;
$font-size-22: 22px;
$font-size-24: 24px;
$font-size-26: 26px;
$font-size-28: 28px;
$font-size-30: 30px;
$font-size-32: 32px;
$font-size-34: 34px;
$font-size-36: 36px;

// Line Height
$line-height-14: 14px;
$line-height-16: 16px;
$line-height-18: 18px;
$line-height-19: 19px;
$line-height-20: 20px;
$line-height-21: 21px;
$line-height-23: 23px;
$line-height-40: 40px;
$line-height-42: 42px;
$line-height-45: 45px;

// Font weights
$font-weight-thin: 100;
$font-weight-extra-light: 200;
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semi-bold: 600;
$font-weight-bold: 700;
$font-weight-extra-bold: 800;
$font-weight-black: 900;
