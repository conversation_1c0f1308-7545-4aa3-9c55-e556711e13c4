.tag-containers {
  padding-left: 26px;
  padding-right: 30px;

  .right-section {
    position: relative;
    width: 200px;
    padding-bottom: 5px;
    padding-left: 62px;

    .published {
      opacity: 0.5;
      pointer-events: none;
    }

    .publish-btn {
      width: 130px;

      .text {
        position: relative;
        left: 14px;
        top: 4px;
        font-weight: 700;
        font-size: 16px;
        color: $black;
      }

      .switch {
        position: relative;
        display: inline-block;
        width: 42px;
        height: 26px;
        margin-left: 20px;

        input {
          opacity: 0;
          width: 0;
          height: 0;
        }
      }

      .slider-round {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: $light-white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
        border-radius: 30px;

        &:before {
          position: absolute;
          content: "";
          height: 23px;
          width: 23px;
          left: 2px;
          bottom: 2px;
          background-color: $white;
          -webkit-transition: 0.4s;
          transition: 0.4s;
          border-radius: 50%;
        }
      }

      input {
        &:checked {
          + {
            .slider-round {
              background-color: $green;

              &:before {
                -webkit-transform: translateX(15px);
                -ms-transform: translateX(15px);
                transform: translateX(15px);
              }
            }
          }
        }

        &:focus {
          + {
            .slider-round {
              box-shadow: 0 0 1px $green;
            }
          }
        }
      }
    }
  }

  .tag-content-mains {
    background: $white;
    border: 1px solid $grainsboro;
    border-radius: 8px;
    padding: 20px;
    width: 100%;
    margin-bottom: 20px;

    .tag-form-titles {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid $sliver;
      padding-bottom: 1px;

      .tag-form-texts {
        display: inline-block;
        font-size: 20px;
        font-weight: 700;
        color: $black;
        padding: 5px;
        padding-bottom: 8px;
        width: 100%;
      }
    }

    .add-title-container {
      position: relative;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      border-bottom: 2px solid $sliver;
      background: $transparent;
      .tag-name-input-section{
        width: 100%;
      }

      .add-title-text {
        color: $black;
        font-size: 24px;
        font-weight: 700;
        width: 100%;
        border: none;
        background: $transparent;
        text-overflow: ellipsis;
      }

      .disable-delete {
        opacity: 0.5;
        pointer-events: none;
      }

      .delete-btn {
        position: relative;
        min-width: 130px;
        padding-bottom: 6px;
        .disable {
          pointer-events: none;
        }
        #disable-button {
          opacity: 0.5;
          pointer-events: none;
        }
        #enable-button {
          opacity: 1;
          pointer-events: all;
          cursor: pointer;
        }

        .delete-tag {
          margin-bottom: 4px;
          .image {
            position: relative;
            width: 17px;
            height: 18px;
            left: 8px;
            bottom: 3px;
          }

          .delete-text {
            position: relative;
            left: 15px;
            font-weight: 700;
            font-size: 15px;
            color: $fiery-red-blaze;
          }
        }
      }

      .disable-tag {
        cursor: default;
      }
    }

    .tag-variant-section {
      .tag-variants-main {
        display: flex;
        justify-content: space-between;
        margin-top: 26px;

        .add-variant-section {
          position: relative;

          .add-variant-main {
            display: flex;
            position: relative;
            right: 8px;
            cursor: pointer;

            .add-variant-btn {
              height: 18px;
              width: 18px;
              margin-top: -1px;
            }

            .add-variant-text {
              font-size: 14px;
              font-weight: 700;
              text-transform: uppercase;
              color: $green-light;
              position: relative;
              top: 2px;
              left: 5px;
            }
            .disable-add-variant-main {
              opacity: 0.5;
            }
          }
        }

        .tag-variants {
          font-weight: 400;
          font-size: 20px;
          color: $black;
        }

        .add-variant-main {
          display: flex;
          position: relative;
          right: 8px;
          cursor: pointer;

          .add-variant-btn {
            height: 18px;
            width: 18px;
          }

          .add-variant-text {
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
            color: $green-light;
            position: relative;
            top: 2px;
            left: 5px;
          }
        }

        .disable-add-variant-main {
          opacity: 0.5;
          pointer-events: none;
        }
      }

      .add-tag-variant {
        font-weight: 400;
        font-size: 16px;
        color: $grey;
        padding-top: 12px;
      }

      .tag-variant-card-main {
        position: relative;
        width: 100%;
        display: grid;
        grid-template-columns: 25% 25% 25% 25%;
        grid-auto-rows: auto;
        padding-top: 12px;
      }
    }
  }
}
