// Import styles here

/*----------------------------------------------------------------
  Common
 */
@import "common/palettes";
@import "common/settings";
@import "common/fonts";
@import "common/reset";
@import "common/base";
@import "common/keyframes";
@import "common/animations";
@import "common/utils";
@import "logo";
@import "slider-toggle-button";
@import "shell";
@import "section";
@import "select-the-language";
@import "size-limit";
@import "updating-live";
@import "floating-notification-popup";
@import "invalid-image-video-popup";
@import "report-problem-popup";

/*----------------------------------------------------------------
  UI elements
 */
@import "ui/colors";
@import "ui/typography";
@import "ui/checkbox";
@import "ui/round-checkbox";
@import "ui/radio";
@import "ui/form-fields";
@import "ui/btn";
@import "ui/main";
@import "ui/wrapper";
@import "ui/simple-data-tooltip";

/*----------------------------------------------------------------
  Components
 */
@import "components/header";
@import "components/nav";
@import "components/global-modal";
@import "components/modal";
@import "components/sidebar";
@import "components/pagination";
@import "components/add-recipe-modal";
@import "components/unauthorized";
@import "components/delete-modal";
@import "components/deleting-modal";
@import "components/save-and-cancel-modal";
@import "components/confirm-replacement-modal";
@import "components/unable-to-content-modal";
@import "components/loader";
@import "components/unable-to-unpublish";
@import "components/access-denied-popup";
@import "components/master-table";
@import "components/add-variant";
@import "components/recipe-preview-detail";
@import "components/recipe-preview";
@import 'components/error';
@import "components/content-wrapper";
@import "components/block-wrapper";
@import 'components/loading-block';
@import 'components/no-result-found';
@import 'components/export-recipe';
@import 'components/search-bar';
@import 'components/import-recipe';
@import 'components/technical-issue';
@import 'components/recipes-count';
@import 'components/invite-user-popup';
@import "components/footer";
@import 'components/terms-of-service';
@import 'components/privacy-policy';
@import 'components/shoppable-review-ingredient-section';
@import "components/video-player";
@import "components/recipe-video";
@import "components/recipe-primary-images";
@import "components/upload-loader";
@import "components/image-zoom-popup";
@import "components/selector-button";
@import "components/recipe-image-video-section";
@import "components/recipe-serving-description-section";
@import "components/upload-media-section";
@import "components/recipe-video-section";
@import "components/recipe-generate-section";
@import "components/recipe-image-generator";
@import "components/recipe-image-selector-box";
@import "components/media-empty-box";
@import "components/batch-generator";
@import "components/recipe-title-section";
@import "components/recipe-preview/recipe-preview-variant-section";
@import "components/recipe-preview/recipe-preview-head-section";
@import "components/recipe-preview/recipe-preview-media-section";
@import "components/recipe-preview/recipe-preview-steps-section";
@import "components/recipe-preview/recipe-preview-ingredient-section";
@import "components/recipe-preview/recipe-preview-filters-section";
@import "components/recipe-preview/recipe-preview-slug-section";
@import "components/recipe-preview/recipe-preview-nutrition-section";
@import "components/recent-activity";
@import "components/recipe-type-badge";
@import "components/simple-table";
@import "components/badge";
@import "components/simple-actions";
@import "components/languages-alert";
@import "components/image-select-button";
@import "components/expansion-panel";
@import "components/body-menu";
@import "components/variant-card-field";
@import "components/alert-modal";
@import "components/confirm-modal";
@import "components/process-modal";
@import "components/calendar-picker";
@import "components/invalid-file-modal";
@import "components/page-top-block";
@import "components/page-actions";
@import "components/simple-content-wrapper";
@import "components/image-box";
@import "components/name-field";
@import "components/button-with-switcher";
@import "components/select-option";
@import "components/text-field";
@import "components/url-field";
@import "components/page-header-with-calendar";
@import "components/simple-sticky-wrapper";
@import "components/filter-select";

// Recipes
@import "recipes"; // Will be deleted together while working on task IQ-795
@import "edit-products";
@import "recipe-detail";
@import "add-instruction";
@import "recipe-status";
@import "recipe-schedule-status"; // Will be deleted together while working on task IQ-795
@import "recipe-steps";

// Generator
@import "generator";

// Search Filters
@import "edit-search";

// Tags
@import "tags";
@import "edit-tag";
@import "add-tags";

// Ingredients
@import "ingredients";
@import "edit-product-matches";
@import "shoppable-review";
@import "add-ingredient-group-step";

// Organizations
@import "organizations";
@import "add-edit-organizations";

// Heros
@import "dynamic-hero";
@import "edit-dynamic-hero";
@import "add-edit-dynamic-news";
@import "add-edit-dynamic-quiz";
@import "add-dynamic-event";
@import "edit-dynamic-event";
@import "add-dynamic-content";
@import "edit-dynamic-content";
@import "add-edit-dynamic-advice";

// Videos
@import "roche-video";

// Banners
@import "banner-details";
@import "banner";

//collections scsss
@import 'collection-delete-modal';
@import 'collection-save-modal';
@import 'collections';
@import 'collection-details';
@import 'collection-form-section';
@import 'collection-tag-details-section';
@import 'detail-page-top-section';

/*----------------------------------------------------------------
  Pages
 */
@import "pages/recipe-generator";
@import "pages/foodlm-generator";
@import "pages/settings-page";
// Login, Signup
@import "pages/auth";
@import "pages/iq-users";
@import "pages/overview";
@import "pages/create-project";
// Category Groups
@import "pages/cat-group";
@import "edit-categories-group";
// Articles
@import "pages/articles";
// Recipes
@import "pages/recipes";
// Category
@import "pages/category";
