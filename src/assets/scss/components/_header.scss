.header {
  background: $pristine-white;
  position: fixed;
  width: 100%;
  height: 70px;
  top: 0;
  left: 0;
  right: 0;
  z-index: 60;
  padding-bottom: 10px;
}

.header .shell {
  max-width: 1180px;
  padding: 0;
  margin-left: 200px;
}

.header .profile-icon {
  position: absolute;
  right: 65px;
  top: 20px;
  cursor: pointer;
  height: 24px;
  width: 24px;
  font-family: $font-family-averta;

  &:active {
    background-color: $granny-apple;
  }

  .user-card {
    background-color: lightgreen;
    border-radius: 50px;
    height: 37px;
    width: 37px;
    display: flex;
    justify-content: center;
    align-items: center;

    .user-name {
      font-size: $font-size-19;
      font-weight: 600;
      color: $white;
      text-transform: uppercase;
    }
  }
}

.disable-profile-icon {
  opacity: 0.3;
  pointer-events: none;
}

.selected {
  background-color: $granny-apple;
  border-radius: 50%;
}



.header .header-aside {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}

.header .header-actions {
  margin-left: 31px;
  padding-top: 4px;
}

.modal {
  font-family: $font-family-averta;
  .button-container {
    .create-btn {
      padding: 10px 25px;
      border-radius: 30px;
      color: $white;
      text-transform: uppercase;
      margin: 40px 5px 0px 5px;
      box-shadow: 0 0 13px $box-shadow;
      background: $crimson-blaze;
      border: none;
      font-weight: 700;
      font-size: 14px;
    }

    .cancel-btn {
      padding: 10px 25px;
      border-radius: 30px;
      color: $green;
      text-transform: uppercase;
      margin: 40px 5px 0px 5px;
      box-shadow: 0 0 13px $box-shadow;
      background: $white;
      border: 1px solid $subtle-whisper-grey;
      font-weight: 700;
      font-size: 14px;
    }
  }
}

.user-info-list {
  right: 10px;
  top: 65px;
  z-index: 20000;
  position: absolute;
  overflow: visible;
  visibility: visible;
  border-radius: 4px;
  background: $white;
  box-shadow: 0 4px 10px 0 $shadow-black, 0 3px 5px 0 $faint-black,
    0 0 0 1px $shadowy-black;
  color: $charcoal-light;
  padding: 4px 4px;
  margin: 5px 0 0 1px !important;
  width: 300px;

  li {
    display: block;
  }

  .info-section-container {
    margin: 4px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid $subtle-whisper-grey;

    &:last-child {
      padding-bottom: 0;
      border-bottom: none;
    }

    .main-section {
      display: flex;
      align-items: center;
      padding: 5px;
      border-radius: 3px;
      cursor: pointer;

      &:hover {
        background-color: $green;
        color: $white;
      }

      .icon {
        height: 24px;
        width: 24px;

        img {
          opacity: 0.9;
          height: 100%;
          width: 100%;
        }
      }

      .text {
        font-weight: 400;
        font-size: 15px;
        line-height: 27px;
        margin-left: 12px;
      }
    }
  }
}

.project-container {
  position: absolute;
  right: 110px;
  top: 16px;
  height: 44px;
  width: 185px;
  font-family: $font-family-averta;

  .project-input-section {
    display: flex;
    position: relative;
    background: $white;
    box-shadow: 1px 1px 0px 0px $box-shadow;
    border-radius: 8px;
    height: 100%;
    width: 100%;
    padding: 0px 15px;

    .project-dropdown {
      display: flex;
      justify-content: space-between;
      width: 100%;
      align-items: center;
      cursor: pointer;
    }

    .project-name-text {
      color: $graphite-gray;
      line-height: 20px;
      font-family: $font-family-averta;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      width: 90%;
    }

    .arrow {
      display: flex;
      width: 8px;
      height: 12px;

      .project-dropdown-icon {
        transform: rotate(90deg);
        cursor: pointer;
        height: 100%;
        width: 100%;
      }
    }

    .project-autocomplete-results {
      position: absolute;
      list-style: none;
      top: 46px;
      right: 2px;
      box-shadow: 0 1px 10px 0 $box-shadow;
      background: $white;
      z-index: 1;
      color: $charcoal-light;
      max-height: 185px;
      width: 182px;
      border-radius: 4px;
      padding: 2px;
    }

    .search-project-container {
      position: sticky;
      top: 0;
      background: $white;
      z-index: 2;
      display: flex;
      align-items: center;
      padding: 10px 6px;
      border-bottom: 1px solid $grainsboro;

      img {
        width: 16px;
        height: 18px;
      }

      input {
        border: none;
        margin-left: 5px;
        max-width: 76%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .project-list-container {
      max-height: 140px;
      overflow-y: auto;
      scrollbar-color: $grainsboro $whispering-white-smoke;
      scrollbar-width: thin;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: $whispering-white-smoke;
      }

      &::-webkit-scrollbar-thumb {
        background: $grainsboro;
        background-clip: content-box;
      }
    }

    .project-autocomplete-result {
      color: $black;
      cursor: pointer;
      margin: 2px;
      display: flex;
      border-radius: 4px;
      .select-project-button {
        padding: 14px 6px;
        width: 100%;
        text-align: left;
      }

      &.is-active,
      &:hover {
        background: $green;
        color: $white;
      }
    }

    .project-result-name {
      text-align: left;
      word-break: break-word;
    }
    .no-result-section {
      display: flex;
      justify-content: center;
      padding: 10px;
    }
  }
}
