<template>
  <div class="image-input-container">
    <div :class="baseClass + '-content-input'">
      <div
        :class="[baseClass + '-content-input-container', { 'simple-data-tooltip': hasContentNameFocus }]"
        :data-tooltip-text="hasContentNameFocus && localContentName"
      >
        <input
          :class="baseClass +'-content-input-text'"
          id="contentNameRef"
          v-model.trim="localContentName"
          placeholder="Content name in CMS"
          autocomplete="off"
          @mouseover="checkContentName"
          @keydown="hideContentTip"
          @mouseleave="hideContentTip"
          @input="onNameInput"
        />
        <span v-if="!localContentName" class="asterisk-input">*</span>
      </div>
      <div class="dynamic-hero-quiz-result-head-section">
        <div class="dynamic-hero-content-main">
          <div class="dynamic-hero-quiz-result-heading">
            <span class="heading-text">Type of content</span>
          </div>
          <div class="dynamic-hero-quiz-result-radio-button-section">
            <div
              class="radio-button-section"
              v-for="(data, index) in contentList"
              :key="index"
              @click="$emit('selectContent', data, data.name)"
            >
              <div class="round">
                <input v-if="data.isChecked" type="radio" />
                <label aria-label="content" for="content-form"></label>
              </div>
              <div class="result-text">{{ data.name }}</div>
            </div>
          </div>
        </div>
        <div
          v-if="!isReplaceLiveHero && !isHeroLive && isEditPage"
          class="delete-button-container"
        >
          <div class="delete" @click="$emit('deleteEvent')">
            <img
              alt="delete"
              src="@/assets/images/delete-icon.png"
              width="15"
              height="16"
            />
            <span>{{ $t('DYNAMIC_HERO.DELETE_CONTENT') }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="!isReplaceLiveHero && !isHeroLive" class="publish-button">
      <div class="schedule-btn">
        <span :class="selectedDate == '' ? 'text disabled-text' : 'text'">
          {{ $t("DYNAMIC_HERO.SCHEDULE") }}
        </span>
        <label
          class="switch"
          :class="{
            'simple-data-tooltip simple-data-tooltip-edge': !selectedDate,
          }"
          :data-tooltip-text="!selectedDate && $t('DYNAMIC_HERO.HERO_SCHEDULE_TOOLTIP')"
        >
          <input
            type="checkbox"
            :checked="!!selectedDate && !!isEventStatusDisplayed"
            :disabled="!selectedDate"
            @change="onStatusChange"
          />
          <span
            :class="
              selectedDate == ''
                ? 'slider-round disabled-slider'
                : 'slider-round'
            "
          ></span>
        </label>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  contentName: String,
  contentList: Array,
  selectedDate: {
    type: [Date, Array, String, Object],
    default: null
  },
  isReplaceLiveHero: Boolean,
  isHeroLive: Boolean,
  isEventStatusDisplayed: Boolean,
  hasContentNameFocus: Boolean,
  baseClass: String,
  isEditPage: Boolean,
  checkContentName: Function,
  hideContentTip: Function,
});

const emit = defineEmits([
  "update:contentName",
  "selectContent",
  "scheduleToggle",
  "deleteEvent",
]);

const onStatusChange = (event) => {
  emit("scheduleToggle", event.target.checked);
};

const onNameInput = (event) => {
  emit("update:contentName", event.target.value);
  props.hideContentTip();
};
const localContentName = ref(props.contentName);


</script>
