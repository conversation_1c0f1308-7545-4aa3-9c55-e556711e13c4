<template>
  <div class="result-not-found">
    <p class="text-title-2 font-bold">{{ $t('COMMON.NO_RESULTS_FOUND') }}.</p>
    <p v-if="isReloadRequired">{{ $t('COMMON.RELOAD_PAGE') }}</p>
    <p v-if="!isContentSearched">{{ noResultText }}</p>
  </div>
</template>
<script setup>
defineProps({
  isReloadRequired: {
    type: Boolean,
    default: false,
  },
  noResultText: {
    type: String,
    default: "",
  },
  isContentSearched: {
    type: Boolean,
    default: false,
  },
});
</script>
