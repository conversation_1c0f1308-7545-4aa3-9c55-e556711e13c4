{"config": {"init": {"clients": {"fss": {"host": "https://int-apigw.inh0.net/fss", "endpoints": {"getAllFoodItemIsin": "/api/v2.1/food-items", "getSearchProducts": "/api/v1.0/products/search", "getProducts": "/api/v2.1/products", "getServingScale": "/api/v1.0/ingredients/_scale", "postIngredientRawText": "/api/v1/ingredients/construct", "getIngredientsStatus": "/api/v1.2/ingredients/{isin}"}}, "fmp": {"host": "https://int-apigw.inh0.net/fmp", "endpoints": {"getIngredientsCampaign": "/api/v1.0/ingredients/-/campaigns"}}, "icl": {"host": "https://int-apigw.inh0.net/icl", "endpoints": {"getRecipeCSV": "/api/v1.4/recipes/_extract", "getRecipeList": "/api/v1.3/recipes/search", "getRecipe": "/api/v1.3/recipes", "getCategoryStatistics": "/api/v1.2/recipes/-/groups/statistics", "saveTag": "/api/v1.2/recipes/-/groups", "getOperationStatus": "/api/v1.0/operations/{opId}", "getCategoriesNames": "/api/v1.0/category-groups", "getRecipeCategories": "/api/v1/recipes/-/groups/search", "getRecipeTags": "/api/v1/recipes/-/groups/search", "getPreSignedUrl": "/api/v1/assets/{isin}/presigned-url", "saveRecipe": "/api/v1.3/recipes", "publishRecipe": "/api/v1.3/recipes/{isin}/_publish", "unPublishRecipe": "/api/v1.3/recipes/{isin}/_unpublish", "getCategorySlug": "/api/v1/categories", "getCategoryGroupList": "/api/v1.2/recipes/-/groups/search", "postProductSuggestionIssue": "/api/v1.1/recipes/product-suggestion-issues/report", "getIngredient": "/api/v1.0/recipes/-/ingredients"}}, "module": {"host": "https://int-apigw.inh0.net/module", "endpoints": {"getCollectionList": "recipe/iq/featured"}}, "flite": {"host": "https://int-apigw.inh0.net/flite", "endpoints": {"generatorData": "/v1/recipes/generator/data", "getRecipesCount": "/v1/overview/statistics/iq", "postImportRecipe": "/v1/parser", "getVideos": "v1/videos", "getTagMultiData": "v1/recipes/tags", "getEditSearch": "v1/configs/search", "getSearchOrganizations": "v1/organizations/search", "postIngredientCampaign": "v1/ingredients/campaign", "getOrganizationsData": "v1/organizations", "deleteOrganization": "v1/organizations", "postOrganizations": "v1/organizations", "getPreSignedUrlImage": "v1/assets/{isin}/presigned-url", "getNewIsins": "/v1/isins", "getRecipeDiets": "/v1/recipes/diets/search", "getRecipeSlug": "/v1/recipes/{isin}/generate-slug", "deleteRecipe": "/v1/recipes/{isin}", "getRecipeAllergens": "/v1/recipes/allergens/search", "getRecipeUnitConfig": "/v1/configs/unit", "getCategoryData": "/v1/recipes/categories", "getTagData": "/v1/recipes/tags", "getTagAssociations": "/v1/recipes/tags/{isin}/associations", "getCategoryAssociations": "/v1/recipes/categories/{isin}/associations", "getCategoryMasterData": "/v1/recipes/categories/search", "getTagMasterData": "/v1/recipes/tags/search", "getCategoryGroupMasterData": "/v1/recipes/category-groups/search", "getIngredientLocked": "/v1/ingredients/campaign/locked", "getIngredientUnlocked": "/v1/ingredients/campaign/unlocked", "getOverviewDetails": "/v1/overview/statistics", "getNutritionalData": "/v1/configs/nutrient", "calculateNutritionalDVP": "/v1/nutrients/dvp", "getingredientKeywords": "/v1/ingredients/data", "postingredientKeywords": "/v1/ingredients/data", "multiGetIngredientProducts": "/v1/ingredients/products/multi", "getIngredientProducts": "/v1/ingredients/products", "getIngredientBrands": "/v1/ingredients/products/brands", "getIngredientMatchedProducts": "/v1/ingredients/products/matched", "getIngredientPromotedProducts": "/v1/ingredients/products/promoted", "articleData": "/v1/articles", "articleDataUUID": "/v1/articles/uuid", "articleDataOrder": "/v1/articles/order", "articleCategoriesData": "/v1/articles/categories", "articleCategoriesDataOrder": "/v1/articles/categories/order", "getIngredientReferenceProduct": "/v1/products/", "getShoppableReviewConfig": "v1/configs/shoppable", "getFeatureConfig": "v1/configs/feature", "getLangConfig": "v1/configs/lang", "getDynamicHeroList": "v1/heroes", "getBannerList": "v1/banners", "postAssociateOperation": "v1/operations/foodlab/icl/associate", "postUpdateOperation": "v1/operations/foodlab/icl/update", "getCategoryCampaign": "v1/recipes/categories/{isin}/campaigns", "postCategoryCampaign": "v1/recipes/categories/campaigns", "getProjectsList": "v1/projects", "postProject": "v1/projects", "deleteProject": "v1/projects", "updateProject": "v1/projects", "getRecipeSchedule": "v1/schedules/recipes", "getProjectDetails": "v1/projects/details", "postControl": "v1/projects/permissions", "inviteUser": "/v1/projects/invite", "selfServeVideo": "/v1/videos/getting-started", "postSimpleRecipe": "/v1/recipes/simple", "simplifyRecipe": "/v1/recipes/simplify", "uploadImageFromUrl": "/v1/assets/upload/external-url", "uploadImagesFromUrls": "/v1/assets/upload/external-urls", "getPromptHistory": "v1/recipes/generator/prompt/search", "getExportPromptHistory": "v1/recipes/generator/history", "getBatchPromptList": "v1/recipes/generator/prompts/batch", "recentActivity": "v1/recent-activities", "hiddenItems": "v1/hidden-items", "searchIngredients": "/v1/ingredients/search"}}, "fcs": {"host": "https://int-apigw.inh0.net/fcs", "endpoints": {"postIngredientParse": "/api/ingredients/_parse"}}, "fais": {"host": "https://int-apigw.inh0.net/fais"}}}}}