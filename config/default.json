{"config": {"init": {"clients": {"fss": {"host": "__OVERRIDDEN__", "endpoints": {"getAllFoodItemIsin": "/api/v2.1/food-items", "getSearchProducts": "/api/v1.0/products/search", "getProducts": "/api/v2.1/products", "getServingScale": "/api/v1.0/ingredients/_scale", "postIngredientRawText": "/api/v1/ingredients/construct", "getIngredientsStatus": "/api/v1.2/ingredients/{isin}"}}, "fmp": {"host": "__OVERRIDDEN__", "endpoints": {"getIngredientsCampaign": "/api/v1.0/ingredients/-/campaigns"}}, "icl": {"host": "__OVERRIDDEN__", "endpoints": {"getRecipeCSV": "/api/v1.4/recipes/_extract", "getRecipeList": "/api/v1.3/recipes/search", "getRecipe": "/api/v1.3/recipes", "getCategoryStatistics": "/api/v1.2/recipes/-/groups/statistics", "saveTag": "/api/v1.2/recipes/-/groups", "getOperationStatus": "/api/v1.0/operations/{opId}", "getCategoriesNames": "/api/v1.0/category-groups", "getRecipeCategories": "/api/v1/recipes/-/groups/search", "getRecipeTags": "/api/v1/recipes/-/groups/search", "getPreSignedUrl": "/api/v1/assets/{isin}/presigned-url", "saveRecipe": "/api/v1.3/recipes", "publishRecipe": "/api/v1.3/recipes/{isin}/_publish", "unPublishRecipe": "/api/v1.3/recipes/{isin}/_unpublish", "getCategorySlug": "/api/v1/categories", "getCategoryGroupList": "/api/v1.2/recipes/-/groups/search", "postProductSuggestionIssue": "/api/v1.1/recipes/product-suggestion-issues/report", "getIngredient": "/api/v1.0/recipes/-/ingredients"}}, "module": {"host": "__OVERRIDDEN__", "endpoints": {"getCollectionList": "recipe/iq/featured"}}, "flite": {"host": "__OVERRIDDEN__", "endpoints": {"generatorData": "/v1/recipes/generator/data", "getRecipesCount": "/v1/overview/statistics/iq", "postImportRecipe": "/v1/parser", "getVideos": "v1/videos", "getTagMultiData": "v1/recipes/tags", "getEditSearch": "v1/configs/search", "getSearchOrganizations": "v1/organizations/search", "postIngredientCampaign": "v1/ingredients/campaign", "getOrganizationsData": "v1/organizations", "deleteOrganization": "v1/organizations", "postOrganizations": "v1/organizations", "getPreSignedUrlImage": "v1/assets/{isin}/presigned-url", "getNewIsins": "/v1/isins", "getRecipeDiets": "/v1/recipes/diets/search", "getRecipeSlug": "/v1/recipes/{isin}/generate-slug", "deleteRecipe": "/v1/recipes/{isin}", "getRecipeAllergens": "/v1/recipes/allergens/search", "getRecipeUnitConfig": "/v1/configs/unit", "getCategoryData": "/v1/recipes/categories", "getTagData": "/v1/recipes/tags", "getTagAssociations": "/v1/recipes/tags/{isin}/associations", "getCategoryAssociations": "/v1/recipes/categories/{isin}/associations", "getCategoryMasterData": "/v1/recipes/categories/search", "getTagMasterData": "/v1/recipes/tags/search", "getCategoryGroupMasterData": "/v1/recipes/category-groups/search", "getIngredientLocked": "/v1/ingredients/campaign/locked", "getIngredientUnlocked": "/v1/ingredients/campaign/unlocked", "getOverviewDetails": "/v1/overview/statistics", "getNutritionalData": "/v1/configs/nutrient", "calculateNutritionalDVP": "/v1/nutrients/dvp", "getingredientKeywords": "/v1/ingredients/data", "postingredientKeywords": "/v1/ingredients/data", "multiGetIngredientProducts": "/v1/ingredients/products/multi", "getIngredientProducts": "/v1/ingredients/products", "getIngredientBrands": "/v1/ingredients/products/brands", "getIngredientMatchedProducts": "/v1/ingredients/products/matched", "getIngredientPromotedProducts": "/v1/ingredients/products/promoted", "articleData": "/v1/articles", "articleDataUUID": "/v1/articles/uuid", "articleDataOrder": "/v1/articles/order", "articleCategoriesData": "/v1/articles/categories", "articleCategoriesDataOrder": "/v1/articles/categories/order", "getIngredientReferenceProduct": "/v1/products/", "getShoppableReviewConfig": "v1/configs/shoppable", "getFeatureConfig": "v1/configs/feature", "getLangConfig": "v1/configs/lang", "getDynamicHeroList": "v1/heroes", "getBannerList": "v1/banners", "postAssociateOperation": "v1/operations/foodlab/icl/associate", "postUpdateOperation": "v1/operations/foodlab/icl/update", "getCategoryCampaign": "v1/recipes/categories/{isin}/campaigns", "postCategoryCampaign": "v1/recipes/categories/campaigns", "getProjectsList": "v1/projects", "postProject": "v1/projects", "deleteProject": "v1/projects", "updateProject": "v1/projects", "getRecipeSchedule": "v1/schedules/recipes", "getProjectDetails": "v1/projects/details", "postControl": "v1/projects/permissions", "inviteUser": "/v1/projects/invite", "selfServeVideo": "/v1/videos/getting-started", "postSimpleRecipe": "/v1/recipes/simple", "simplifyRecipe": "/v1/recipes/simplify", "uploadImageFromUrl": "/v1/assets/upload/external-url", "uploadImagesFromUrls": "/v1/assets/upload/external-urls", "getPromptHistory": "v1/recipes/generator/prompt/search", "getExportPromptHistory": "v1/recipes/generator/history", "getBatchPromptList": "v1/recipes/generator/prompts/batch", "recentActivity": "v1/recent-activities", "hiddenItems": "v1/hidden-items", "searchIngredients": "/v1/ingredients/search"}}, "fcs": {"host": "__OVERRIDDEN__", "endpoints": {"postIngredientParse": "/api/ingredients/_parse"}}, "fais": {"host": "__OVERRIDDEN__"}}}, "menus": [{"uniqueId": "overview", "name": "Overview", "icon": "icon-overview.svg", "link": "/overview", "horizontalLine": true}, {"uniqueId": "recipes", "name": "Recipes", "icon": "icon-recipes.svg", "link": "/recipes", "horizontalLine": false}, {"uniqueId": "generator", "name": "Generator", "icon": "Generator.png", "link": "/iq-recipe-generator", "horizontalLine": false}, {"uniqueId": "generatorBatch", "name": "Batch Generator", "icon": "Batch Generator.svg", "link": "/iq-batch-generator", "horizontalLine": false}, {"uniqueId": "importer", "name": "Importer", "icon": "import-icon.svg", "horizontalLine": false}, {"uniqueId": "categories", "name": "Categories", "icon": "sidebar-category.png", "link": "/category", "horizontalLine": false}, {"uniqueId": "categoryGroups", "name": "Cat. Groups", "icon": "cat_group_icon.svg", "link": "/cat-group", "horizontalLine": false}, {"uniqueId": "tags", "name": "Tags", "icon": "tags-icon.svg", "link": "/tags", "horizontalLine": false}, {"uniqueId": "ingredients", "name": "Ingredients", "icon": "white_ingredients.png", "link": "/ingredients", "horizontalLine": false}, {"uniqueId": "featured", "name": "Featured", "icon": "collections.svg", "link": "/collections", "horizontalLine": true}, {"uniqueId": "searchFilters", "name": "Search Filters", "icon": "edit_Image.png", "link": "/edit-search", "horizontalLine": false}, {"uniqueId": "organizations", "name": "Organizations", "icon": "organization.png", "link": "/organizations", "horizontalLine": true}, {"uniqueId": "export", "name": "Export", "icon": "export-icon.png", "horizontalLine": true}, {"uniqueId": "users", "name": "Users", "icon": "organization.png", "link": "/iq-users", "horizontalLine": false}, {"uniqueId": "settings", "name": "Settings", "icon": "settings-icon.png", "link": "/settings", "horizontalLine": false}, {"uniqueId": "help", "name": "Help", "icon": "help-icon.svg", "horizontalLine": false}, {"uniqueId": "heroes", "name": "Heroes", "icon": "phil-logo.png", "link": "/dynamic-hero", "horizontalLine": false}, {"uniqueId": "banners", "name": "Banners", "icon": "banner.png", "link": "/banner", "horizontalLine": false}, {"uniqueId": "articles", "name": "Articles", "icon": "icon-articles.svg", "link": "/articles", "horizontalLine": false}, {"uniqueId": "videos", "name": "Videos", "icon": "Play.png", "link": "/roche-video", "horizontalLine": false}]}}